import logging
import os
# Configure logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

import sys
import requests
import numpy as np
import pandas as pd
import json
import mplfinance as mpf
import matplotlib.pyplot as plt
import tempfile
import traceback
import time
import aiohttp
from datetime import time as datetime_time
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, Poll
from telegram.constants import ParseMode
from telegram.ext import Application, CommandHandler, ContextTypes, MessageHandler, filters, CallbackQueryHandler, CallbackContext, PollAnswerHandler
from education.trading_education import handle_learn_trading_ai # استيراد دالة تعلم التداول بالذكاء الاصطناعي
from datetime import datetime, timedelta
import pytz
import asyncio
import aiohttp
import hmac
import hashlib
from typing import TypeV<PERSON>, Type, Union, Any, Dict, Optional
import telegram
from urllib.parse import urlencode
import shutil
import base64
from cryptography.fernet import Fernet
# import redis  # تم إزالة Redis
from apscheduler.schedulers.asyncio import AsyncIOScheduler
import firebase_admin
from firebase_admin import credentials, firestore
from web3 import Web3
import uuid
from google.cloud.firestore_v1 import FieldFilter
import gc
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from memory_profiler import profile
from concurrent.futures import ThreadPoolExecutor
import threading
import dotenv
import re

# استيراد ملف التكوين

# استيراد الملفات الجديدة
from api_manager import APIManager
from api_validators import verify_binance_api, get_binance_client
from analysis.user_market_data import get_market_data_with_user_api
from analysis.gemini_analysis import analyze_with_gemini, generate_smart_alerts, get_user_api_client, verify_gemini_api
from api_ui import setup_api_keys, show_api_instructions, delete_api_keys_ui, show_api_info, show_platform_selection
from services.system_settings import system_settings
from integrations.paypal_payment import verify_paypal_transaction, activate_subscription, AutomaticPaymentVerifier
from services.handle_payment_verification import handle_payment_verification
from services.free_day_system import free_day_system

# استيراد وحدة تعليم التداول
from education.trading_education import (
    handle_learn_trading_ai,
    handle_message_for_ai_tutor,
    generate_and_send_chapter, # Needed for callback
    start_quiz, # Needed for callback
    handle_quiz_answer, # Needed for poll answers
    user_education_state, # استيراد مؤقت للحالة - يجب تحسينه لاحقًا
    check_gemini_api_key, # Needed for callback
    handle_ask_ai_tutor_button, # استيراد دالة معالجة زر اسأل الذكاء الاصطناعي
    set_firestore_db as set_trading_education_db # استيراد دالة تعيين قاعدة البيانات
)

dotenv.load_dotenv()
# استيراد وحدة تهيئة Firebase
from integrations.firebase_init import initialize_firebase

# تهيئة Firebase
db = initialize_firebase()
if not db:
    logger.error("❌ فشل في تهيئة Firebase، سيتم محاولة التهيئة اليدوية")
    try:
        cred = credentials.Certificate('tradingtelegram-da632-firebase-adminsdk-fbsvc-a67cf6e086.json')
        firebase_admin.initialize_app(cred)
        db = firestore.client()
        logger.info("✅ تم تهيئة Firebase يدويًا بنجاح")
    except Exception as e:
        logger.error(f"❌ فشل في تهيئة Firebase يدويًا: {str(e)}")
        raise ValueError("لم يتم تهيئة قاعدة البيانات بعد")

# تعيين قاعدة بيانات Firestore في gemini_analysis
from analysis import gemini_analysis
gemini_analysis.set_firestore_db(db)

# تعيين قاعدة بيانات Firestore في trading_education
set_trading_education_db(db)

# تهيئة نظام الإعدادات العامة
system_settings.initialize_db(db)

# Configure logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

# تعريف المتغيرات الأساسية
class SystemConfig:
    """فئة للإعدادات الأساسية للنظام"""
    __slots__ = []

    @staticmethod
    def get_env_var(name, default=None):
        # أولاً، نحاول الحصول على القيمة من system_settings
        value = system_settings.get(name, None)
        if value is None:
            # إذا لم نجد القيمة في system_settings، نحاول الحصول عليها من متغيرات البيئة
            value = os.getenv(name)

        if not value and default is None:
            raise ValueError(f"المتغير البيئي {name} مطلوب")
        return value or default

    # يتم الآن قراءة جميع القيم من المتغيرات البيئية أو قاعدة البيانات
    DEVELOPER_ID = str(get_env_var("DEVELOPER_ID", "7839527436"))  # تحويل القيمة إلى نص (str) لضمان المقارنة الصحيحة
    # الحصول على التوكن مباشرة من الإعدادات الحساسة
    BOT_TOKEN = system_settings.get("BOT_TOKEN", None, sensitive=True)
    if not BOT_TOKEN:
        BOT_TOKEN = os.getenv("BOT_TOKEN", "DEFAULT_BOT_TOKEN")  # قيمة افتراضية للاختبار
    # تم تعطيل استخدام مفاتيح Binance API الخاصة بالمطور
    BINANCE_API = {
        'key': None,
        'secret': None
    }
    GITHUB = {
        'token': get_env_var("GITHUB_TOKEN", "DEFAULT_GITHUB_TOKEN"),  # قيمة افتراضية للاختبار
        'repo': get_env_var("GITHUB_REPO", "TradingTelegram"),
        'owner': get_env_var("GITHUB_OWNER", "HoySama")
    }
    # تم إزالة إعدادات Redis واستبدالها بنظام التخزين المؤقت باستخدام Firestore
    DEFAULT_VALUES = {
        'analyses_per_day': int(get_env_var("ANALYSES_PER_DAY", "3")),
        'alerts_per_day': int(get_env_var("ALERTS_PER_DAY", "1")),
        'default_lang': get_env_var("DEFAULT_LANG", "ar")
    }

# تعريف المتغيرات المباشرة
TOKEN = SystemConfig.BOT_TOKEN
TELEGRAM_CHAT_ID = SystemConfig.DEVELOPER_ID
# تم تعطيل استخدام مفاتيح Binance API الخاصة بالمطور
BINANCE_API_KEY = None
BINANCE_API_SECRET = None
GITHUB_TOKEN = SystemConfig.GITHUB['token']
GITHUB_REPO = SystemConfig.GITHUB['repo']
GITHUB_OWNER = SystemConfig.GITHUB['owner']

class BinanceManager:
    """مدير اتصالات Binance API"""
    __slots__ = ['base_url', 'market_data_cache', 'market_data_expiry', 'cache_timeout', 'db']

    def __init__(self):
        # تكوين Event Loop لنظام Windows
        if sys.platform == 'win32':
            import asyncio
            try:
                asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
            except Exception as e:
                logger.warning(f"فشل في تعيين WindowsSelectorEventLoopPolicy: {e}")

        self.base_url = "https://api.binance.com/api/v3"
        self.market_data_cache = {}  # ذاكرة محلية لتخزين بيانات السوق
        self.market_data_expiry = {}  # تواريخ انتهاء صلاحية البيانات المخزنة
        self.cache_timeout = 300  # 5 minutes
        self.db = db  # استخدام قاعدة البيانات Firestore

    def _generate_signature(self, params: dict, api_secret: str) -> str:
        """إنشاء توقيع آمن للطلب"""
        query_string = urlencode(params)
        return hmac.new(
            api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def check_symbol_availability(self, symbol: str) -> bool:
        """التحقق من توفر العملة في Binance"""
        try:
            # تنظيف الرمز وإزالة الفراغات
            symbol = symbol.upper().strip()

            # المحاولة باستخدام الرمز الأصلي أولاً
            url = f"{self.base_url}/exchangeInfo"
            params = {'symbol': symbol}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        symbols = data.get('symbols', [])

                        for symbol_info in symbols:
                            if symbol_info.get('symbol') == symbol and symbol_info.get('status') == 'TRADING':
                                logger.info(f"Symbol {symbol} is available")
                                return True

            # إذا لم يتم العثور على الرمز، حاول إضافة USDT
            if not symbol.endswith('USDT'):
                symbol_with_usdt = f"{symbol}USDT"
                params = {'symbol': symbol_with_usdt}

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            symbols = data.get('symbols', [])

                            for symbol_info in symbols:
                                if symbol_info.get('symbol') == symbol_with_usdt and symbol_info.get('status') == 'TRADING':
                                    logger.info(f"Symbol {symbol} is available as {symbol_with_usdt}")
                                    return True

            logger.warning(f"Symbol {symbol} is not available on Binance")
            return False
        except Exception as e:
            logger.error(f"Error checking symbol availability: {str(e)}")
            return False

    async def get_klines(self, symbol: str, interval: str = '4h', limit: int = 100, user_id: str = None):
        """
        جلب بيانات الشموع مع الذاكرة المحلية

        Args:
            symbol: رمز العملة
            interval: الإطار الزمني (1h, 4h, 1d, 1w)
            limit: عدد الشموع المطلوبة
            user_id: معرف المستخدم (اختياري) - إذا تم توفيره، سيتم استخدام مفاتيح API الخاصة بالمستخدم
        """
        try:
            # التحقق من الذاكرة المحلية
            cache_key = f"klines_{symbol}_{interval}"
            current_time = datetime.now()

            if cache_key in self.market_data_cache and cache_key in self.market_data_expiry:
                if current_time < self.market_data_expiry[cache_key]:
                    return self.market_data_cache[cache_key]

            # التحقق من صحة الإطار الزمني
            valid_intervals = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']
            if interval not in valid_intervals:
                logger.error(f"Invalid interval: {interval}. Using default 4h interval.")
                interval = '4h'

            # التحقق من توفر العملة
            if not await self.check_symbol_availability(symbol):
                logger.error(f"Symbol {symbol} is not available for trading")
                return None

            # محاولة استخدام مفاتيح API الخاصة بالمستخدم إذا كان متاحًا
            if user_id and api_manager:
                try:
                    # الحصول على مفاتيح API الخاصة بالمستخدم
                    api_key, api_secret = await api_manager.get_api_keys(user_id, 'binance')

                    if api_key and api_secret:
                        logger.info(f"استخدام مفاتيح API الخاصة بالمستخدم {user_id} للحصول على بيانات {symbol}")

                        # استخدام مكتبة python-binance مع مفاتيح API المستخدم
                        from binance.client import Client
                        client = Client(api_key, api_secret)

                        # الحصول على بيانات الشموع
                        klines = client.get_klines(
                            symbol=symbol,
                            interval=interval,
                            limit=limit
                        )

                        if klines:
                            # تخزين في الذاكرة المحلية
                            self.market_data_cache[cache_key] = klines
                            self.market_data_expiry[cache_key] = current_time + timedelta(seconds=self.cache_timeout)
                            return klines
                except Exception as user_api_error:
                    logger.warning(f"فشل في استخدام مفاتيح API الخاصة بالمستخدم {user_id}: {str(user_api_error)}")
                    # سنستمر باستخدام API العام

            # جلب البيانات من Binance باستخدام API العام
            url = f"{self.base_url}/klines"
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        # تخزين في الذاكرة المحلية
                        self.market_data_cache[cache_key] = data
                        self.market_data_expiry[cache_key] = current_time + timedelta(seconds=self.cache_timeout)
                        return data
                    else:
                        error_text = await response.text()
                        logger.error(f"Error fetching klines: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"Error getting klines for {symbol}: {str(e)}")
            return None

class BinanceTransactionVerifier:
    """نظام متكامل للتحقق من معاملات Binance"""
    def __init__(self):
        self.base_url = "https://api.binance.com"
        self.network_required = "BEP20"
        self.min_confirmations = 2
        self.tx_cache = {}  # ذاكرة محلية لتخزين نتائج التحقق من المعاملات
        self.tx_cache_expiry = {}  # تواريخ انتهاء صلاحية البيانات المخزنة
        self.cache_timeout = 300  # 5 minutes
        self.db = db  # استخدام قاعدة البيانات Firestore

    def _generate_signature(self, params: dict, api_secret: str) -> str:
        query_string = urlencode(params)
        return hmac.new(
            api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def _get_transaction_details(self, txid: str, user_id: str = None) -> dict:
        """
        الحصول على تفاصيل المعاملة من Binance

        Args:
            txid: معرف المعاملة
            user_id: معرف المستخدم (اختياري) - إذا تم توفيره، سيتم استخدام مفاتيح API الخاصة بالمستخدم
        """
        # محاولة استخدام مفاتيح API الخاصة بالمستخدم إذا كان متاحًا
        api_key = None
        api_secret = None

        if user_id and api_manager:
            try:
                # الحصول على مفاتيح API الخاصة بالمستخدم
                api_key, api_secret = await api_manager.get_api_keys(user_id, 'binance')
            except Exception as e:
                logger.warning(f"فشل في الحصول على مفاتيح API للمستخدم {user_id}: {str(e)}")

        # التحقق من وجود مفاتيح API
        if not api_key or not api_secret:
            logger.warning(f"لا توجد مفاتيح API متاحة للتحقق من المعاملة {txid}")
            return None

        timestamp = int(time.time() * 1000)
        params = {
            'timestamp': timestamp,
            'txId': txid
        }
        signature = self._generate_signature(params, api_secret)
        params['signature'] = signature

        headers = {'X-MBX-APIKEY': api_key}

        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v3/deposit/transaction",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    return await response.json()
                logger.warning(f"فشل في الحصول على تفاصيل المعاملة {txid}: {response.status}")
                return None

    async def verify_transaction_complete(self, txid: str, user_id: str, amount: float = 5.0) -> bool:
        """
        التحقق من اكتمال المعاملة

        Args:
            txid: معرف المعاملة
            user_id: معرف المستخدم
            amount: المبلغ المتوقع

        Returns:
            True إذا كانت المعاملة صالحة، False خلاف ذلك
        """
        cache_key = f"tx_verify_{txid}"
        current_time = datetime.now()

        # التحقق من الذاكرة المحلية
        if cache_key in self.tx_cache and cache_key in self.tx_cache_expiry:
            if current_time < self.tx_cache_expiry[cache_key]:
                return self.tx_cache[cache_key]

        try:
            # التحقق من وجود مفاتيح API للمستخدم
            if api_manager:
                has_binance_api = await api_manager.has_api_keys(user_id, 'binance')
                if not has_binance_api:
                    logger.warning(f"المستخدم {user_id} ليس لديه مفاتيح Binance API للتحقق من المعاملة {txid}")
                    # إرسال إشعار للمستخدم بضرورة إضافة مفاتيح API
                    return False

            # الحصول على تفاصيل المعاملة باستخدام مفاتيح API الخاصة بالمستخدم
            tx_details = await self._get_transaction_details(txid, user_id)
            if not tx_details:
                logger.warning(f"لم يتم العثور على تفاصيل المعاملة {txid} للمستخدم {user_id}")
                return False

            is_valid = (
                tx_details.get('network') == self.network_required and
                abs(float(tx_details.get('amount', 0)) - amount) < 0.01 and
                int(tx_details.get('confirmations', 0)) >= self.min_confirmations and
                tx_details.get('memo', '').strip() == user_id
            )

            # تخزين في الذاكرة المحلية
            self.tx_cache[cache_key] = is_valid
            self.tx_cache_expiry[cache_key] = current_time + timedelta(seconds=self.cache_timeout)

            if is_valid:
                logger.info(f"تم التحقق من صحة المعاملة {txid} للمستخدم {user_id}")
            else:
                logger.warning(f"المعاملة {txid} للمستخدم {user_id} غير صالحة")

            return is_valid

        except Exception as e:
            logger.error(f"خطأ في التحقق من المعاملة {txid}: {str(e)}")
            return False

class SecureBackupSystem:
    """نظام نسخ احتياطي آمن مع تشفير متقدم"""
    def __init__(self):
        self.encryption_key = None
        self.key_rotation_interval = timedelta(days=7)
        self.last_key_rotation = None

    async def _rotate_encryption_key(self):
        new_key = Fernet.generate_key()
        key_data = {
            'key': new_key.decode(),
            'created_at': datetime.now().isoformat(),
            'previous_key': self.encryption_key.decode() if self.encryption_key else None
        }

        keys_ref = db.collection('backup_keys').document('current')
        keys_ref.set(key_data)

        self.encryption_key = new_key
        self.last_key_rotation = datetime.now()

    async def create_secure_backup(self, data: dict) -> bool:
        try:
            if not self.encryption_key or (
                self.last_key_rotation and
                datetime.now() - self.last_key_rotation >= self.key_rotation_interval
            ):
                await self._rotate_encryption_key()

            # تشفير البيانات
            f = Fernet(self.encryption_key)
            encrypted_data = f.encrypt(json.dumps(data).encode())

            # إضافة تشفير إضافي للبيانات الحساسة
            if 'payment_info' in data or 'user_data' in data:
                salt = os.urandom(16)
                kdf = hashlib.pbkdf2_hmac('sha256', self.encryption_key, salt, 100000)
                additional_key = base64.urlsafe_b64encode(kdf)
                f_additional = Fernet(additional_key)
                encrypted_data = f_additional.encrypt(encrypted_data)
            else:
                salt = None

            # إنشاء البيانات الوصفية
            metadata = {
                'timestamp': datetime.now().isoformat(),
                'checksum': hashlib.sha256(encrypted_data).hexdigest(),
                'salt': base64.b64encode(salt).decode() if salt else None,
                'key_id': db.collection('backup_keys').document('current').id
            }

            # حفظ النسخة الاحتياطية في Firestore
            backup_ref = db.collection('backups').document()
            backup_ref.set({
                'encrypted_data': base64.b64encode(encrypted_data).decode(),
                'metadata': metadata
            })

            return True

        except Exception as e:
            logger.error(f"Error creating secure backup: {str(e)}")
            return False

class AutomaticTransactionVerifier:
    """نظام التحقق التلقائي من المعاملات"""
    def __init__(self):
        self.binance_verifier = BinanceTransactionVerifier()
        self.verification_interval = 60  # ثانية
        self.max_verification_attempts = 5
        self.scheduler = AsyncIOScheduler()
        self.active_verifications = {}

    async def start_verification(self, transaction_id: str, user_id: str):
        if transaction_id in self.active_verifications:
            return

        verification_data = {
            'attempts': 0,
            'user_id': user_id,
            'status': 'pending',
            'last_check': None
        }

        self.active_verifications[transaction_id] = verification_data

        self.scheduler.add_job(
            self._verify_transaction,
            'interval',
            seconds=self.verification_interval,
            args=[transaction_id],
            id=f'verify_{transaction_id}'
        )

    async def _verify_transaction(self, transaction_id: str):
        verification_data = self.active_verifications.get(transaction_id)
        if not verification_data:
            return

        verification_data['attempts'] += 1
        verification_data['last_check'] = datetime.now()

        try:
            is_valid = await self.binance_verifier.verify_transaction_complete(
                transaction_id,
                verification_data['user_id']
            )

            if is_valid:
                if await activate_subscription(
                    verification_data['user_id'],
                    transaction_id
                ):
                    verification_data['status'] = 'completed'
                    await self._stop_verification(transaction_id)
                    return

            if verification_data['attempts'] >= self.max_verification_attempts:
                verification_data['status'] = 'failed'
                await self._stop_verification(transaction_id)

        except Exception as e:
            logger.error(f"Error in automatic verification for {transaction_id}: {str(e)}")
            verification_data['status'] = 'error'
            await self._stop_verification(transaction_id)

    async def _stop_verification(self, transaction_id: str):
        try:
            self.scheduler.remove_job(f'verify_{transaction_id}')
        except:
            pass

        verification_data = self.active_verifications.pop(transaction_id, None)
        if verification_data:
            transaction_ref = db.collection('transactions').document(transaction_id)
            await transaction_ref.update({
                'status': verification_data['status'],
                'verified_at': datetime.now().isoformat(),
                'verification_attempts': verification_data['attempts']
            })

# تهيئة الأنظمة
binance_manager = BinanceManager()
binance_verifier = BinanceTransactionVerifier()
secure_backup = SecureBackupSystem()
auto_verifier = AutomaticTransactionVerifier()

# استيراد نظام التخزين المؤقت باستخدام Firestore
from utils.firestore_cache import FirestoreCache

# تهيئة نظام التخزين المؤقت
firestore_cache = FirestoreCache(db)

# تهيئة مدير API
# الحصول على مفتاح التشفير من Firestore أو إنشاء مفتاح جديد
async def get_or_create_encryption_key():
    try:
        # التحقق من وجود مفتاح في Firestore
        key_ref = db.collection('system_config').document('encryption_key')
        key_data = key_ref.get()

        if key_data.exists and 'key' in key_data.to_dict() and len(key_data.to_dict()['key']) == 44:
            # استخدام المفتاح المخزن
            return key_data.to_dict()['key']

        # التحقق من وجود مفتاح في ملف .env
        import config as config_module
        env_key = config_module.ENCRYPTION_KEY
        if env_key and len(env_key) == 44:
            # تخزين المفتاح في Firestore
            key_ref.set({'key': env_key, 'created_at': datetime.now().isoformat()})
            return env_key

        # إنشاء مفتاح جديد
        new_key = Fernet.generate_key().decode()
        key_ref.set({'key': new_key, 'created_at': datetime.now().isoformat()})
        logger.info("تم إنشاء مفتاح تشفير جديد وتخزينه في Firestore. يرجى تخزينه في ملف .env للاستخدام المستقبلي.")
        # تم إزالة طباعة مفتاح التشفير هنا لأسباب أمنية
        return new_key
    except Exception as e:
        logger.error(f"خطأ في الحصول على مفتاح التشفير: {str(e)}")
        # في حالة الخطأ، نستخدم مفتاح جديد
        return Fernet.generate_key().decode()

# سيتم تعيين مفتاح التشفير لاحقًا بعد تهيئة النظام
encryption_key = None
api_manager = None

# Global state
user_states = {}
price_alerts = {}
user_settings = {}

class Config:
    """فئة لإدارة إعدادات النظام"""
    _instance = None
    _data = {
        'bot_token': TOKEN,
        'owner_id': SystemConfig.DEVELOPER_ID,  # استخدام المعرف المركزي
        'backup_interval': 24,
        'stats_interval': 12,
        'payment_methods': {
            'paypal': {
                'amount': 5.0,
                'currency': 'USD'
            }
        },
        'PAYMENT_AMOUNT': 5.0  # تحديث السعر هنا
    }

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._initialized = True
            self._load_config()

    def _load_config(self):
        """تحميل الإعدادات من Firestore"""
        try:
            config_ref = db.collection('config').document('settings')
            config_data = config_ref.get()
            if config_data.exists:
                self._data.update(config_data.to_dict())
        except Exception as e:
            logger.error(f"Error loading config from Firestore: {str(e)}")

    def update_config(self, new_data: dict):
        """تحديث الإعدادات"""
        try:
            self._data.update(new_data)
            config_ref = db.collection('config').document('settings')
            config_ref.set(self._data)
        except Exception as e:
            logger.error(f"Error updating config in Firestore: {str(e)}")

    @property
    def bot_token(self) -> str:
        return self._data.get('bot_token', TOKEN)

    @property
    def owner_id(self) -> str:
        return SystemConfig.DEVELOPER_ID  # استخدام المعرف المركزي دائماً

    @property
    def backup_interval(self) -> int:
        return self._data.get('backup_interval', 24)

    @property
    def stats_interval(self) -> int:
        return self._data.get('stats_interval', 12)

    @property
    def payment_methods(self) -> dict:
        return self._data.get('payment_methods', {})

class SubscriptionSystem:
    def __init__(self):
        """تهيئة نظام الاشتراكات"""
        self.cache_timeout = 3600  # ساعة واحدة

    # متغيرات لتخزين البيانات في الذاكرة المحلية
    _subscription_cache = {}
    _subscription_cache_expiry = {}
    _settings_cache = {}
    _settings_expiry = {}
    _free_usage_cache = {}
    _free_usage_expiry = {}

    async def has_api_keys(self, user_id: str, api_type: str) -> bool:
        """التحقق من وجود مفاتيح API للمستخدم"""
        return await api_manager.has_api_keys(user_id, api_type)

    async def get_subscription_info(self, user_id: str, lang: str = 'ar') -> str:
        """الحصول على معلومات الاشتراك"""
        is_subscribed = self.is_subscribed(user_id)
        expiry_date = self.get_subscription_expiry(user_id)

        # التحقق من وجود مفاتيح API
        has_binance_api = await self.has_api_keys(user_id, 'binance')
        has_gemini_api = await self.has_api_keys(user_id, 'gemini')

        if lang == 'ar':
            status = "مشترك ✅" if is_subscribed else "غير مشترك ❌"
            api_status = (
                f"• Binance API: {'متصل ✅' if has_binance_api else 'غير متصل ❌'}\n"
                f"• Gemini API: {'متصل ✅' if has_gemini_api else 'غير متصل ❌'}"
            )

            return f"""💎 نظام الاشتراك

معرف المستخدم: {user_id}
الحالة: {status}
تاريخ الانتهاء: {expiry_date if is_subscribed else 'غير متوفر'}

🔑 حالة API:
{api_status}

⚠️ ملاحظات مهمة:
• لا يوجد دعم فني - اتبع التعليمات بدقة
• أنت المسؤول الوحيد عن عملية التحويل
• الاشتراك غير قابل للاسترداد
• التحويل يعني قبول جميع الشروط
• عدم اتباع الخطوات قد يؤدي لفقدان الاشتراك"""
        else:
            status = "Subscribed ✅" if is_subscribed else "Not Subscribed ❌"
            api_status = (
                f"• Binance API: {'Connected ✅' if has_binance_api else 'Not Connected ❌'}\n"
                f"• Gemini API: {'Connected ✅' if has_gemini_api else 'Not Connected ❌'}"
            )

            return f"""💎 Subscription System

User ID: {user_id}
Status: {status}
Expiry Date: {expiry_date if is_subscribed else 'Not Available'}

🔑 API Status:
{api_status}

⚠️ Important Notes:
• No technical support - follow instructions carefully
• You are solely responsible for the transfer
• Subscription is non-refundable
• Transfer implies acceptance of all terms
• Not following steps may result in subscription loss"""

    def get_subscription_expiry(self, user_id: str) -> str:
        """الحصول على تاريخ انتهاء الاشتراك"""
        try:
            subscription = self.get_subscription_details(user_id)
            if subscription and subscription.get('is_active', False) and subscription.get('expiry'):
                return subscription['expiry']
            return None
        except Exception as e:
            logger.error(f"خطأ في الحصول على تاريخ انتهاء الاشتراك: {str(e)}")
            return None

    def get_subscription_status(self, user_id: str, full_details: bool = False) -> Union[bool, dict]:
        """
        دالة موحدة للتحقق من حالة الاشتراك
        إذا كان full_details=False، تعيد قيمة منطقية فقط
        وإذا كان full_details=True، تعيد قاموس بكامل التفاصيل

        تأخذ في الاعتبار حالة اليوم المجاني الأسبوعي
        """
        try:
            # التحقق من الذاكرة المحلية أولاً
            current_time = datetime.now()

            # تحقق من وجود متغيرات الذاكرة المحلية
            if hasattr(self, '_subscription_cache') and hasattr(self, '_subscription_cache_expiry'):
                if user_id in self._subscription_cache and user_id in self._subscription_cache_expiry:
                    if current_time < self._subscription_cache_expiry[user_id]:
                        # استخدام البيانات من الذاكرة المحلية بدون تسجيل

                        # التحقق من اليوم المجاني
                        try:
                            # إذا كان المستخدم غير مشترك، نتحقق من اليوم المجاني
                            if not self._subscription_cache[user_id]['is_active']:
                                # استخدام الدالة الجديدة is_free_day_active
                                if free_day_system.is_free_day_active(user_id):
                                    # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                                    temp_subscription = self._subscription_cache[user_id].copy()
                                    temp_subscription['is_active'] = True
                                    temp_subscription['is_free_day'] = True
                                    temp_subscription['features'] = self.get_premium_features()

                                    if not full_details:
                                        return True
                                    return temp_subscription
                        except Exception as free_day_error:
                            logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

                        if not full_details:
                            return self._subscription_cache[user_id]['is_active']
                        return self._subscription_cache[user_id]
            else:
                # تهيئة متغيرات الذاكرة المحلية إذا لم تكن موجودة
                self._subscription_cache = {}
                self._subscription_cache_expiry = {}

            # التحقق من Firestore
            try:
                user_ref = db.collection('subscriptions').document(user_id)
                user_data = user_ref.get()

                if not user_data.exists:
                    # لا يوجد اشتراك في Firestore
                    subscription_data = {
                        'is_active': False,
                        'expiry': None,
                        'features': self.get_free_features()
                    }
                    # تخزين في الذاكرة المحلية
                    self._subscription_cache[user_id] = subscription_data
                    self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

                    # التحقق من اليوم المجاني
                    try:
                        # استخدام الدالة الجديدة is_free_day_active
                        if free_day_system.is_free_day_active(user_id):
                            # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                            temp_subscription = subscription_data.copy()
                            temp_subscription['is_active'] = True
                            temp_subscription['is_free_day'] = True
                            temp_subscription['features'] = self.get_premium_features()

                            if full_details:
                                return temp_subscription
                            return True
                    except Exception as free_day_error:
                        logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

                    if full_details:
                        return subscription_data
                    return False

                subscription = user_data.to_dict()

                # التحقق من وجود تاريخ الانتهاء
                if 'expiry' not in subscription:
                    subscription_data = {
                        'is_active': False,
                        'expiry': None,
                        'features': self.get_free_features()
                    }
                    # تخزين في الذاكرة المحلية
                    self._subscription_cache[user_id] = subscription_data
                    self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

                    # التحقق من اليوم المجاني
                    try:
                        # استخدام الدالة الجديدة is_free_day_active
                        if free_day_system.is_free_day_active(user_id):
                            # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                            temp_subscription = subscription_data.copy()
                            temp_subscription['is_active'] = True
                            temp_subscription['is_free_day'] = True
                            temp_subscription['features'] = self.get_premium_features()

                            if full_details:
                                return temp_subscription
                            return True
                    except Exception as free_day_error:
                        logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

                    if full_details:
                        return subscription_data
                    return False

                try:
                    expiry = datetime.fromisoformat(subscription['expiry'])
                    is_active = expiry > current_time

                    # تخزين في الذاكرة المحلية
                    subscription_data = {
                        'is_active': is_active,
                        'expiry': expiry.isoformat() if is_active else None,
                        'features': self.get_premium_features() if is_active else self.get_free_features()
                    }

                    # تخزين في الذاكرة المحلية
                    self._subscription_cache[user_id] = subscription_data
                    self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

                    if full_details:
                        return subscription_data
                    return is_active

                except (ValueError, TypeError):
                    # خطأ في تنسيق تاريخ الانتهاء
                    subscription_data = {
                        'is_active': False,
                        'expiry': None,
                        'features': self.get_free_features()
                    }
                    # تخزين في الذاكرة المحلية
                    self._subscription_cache[user_id] = subscription_data
                    self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

                    # التحقق من اليوم المجاني
                    try:
                        # استخدام الدالة الجديدة is_free_day_active
                        if free_day_system.is_free_day_active(user_id):
                            # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                            temp_subscription = subscription_data.copy()
                            temp_subscription['is_active'] = True
                            temp_subscription['is_free_day'] = True
                            temp_subscription['features'] = self.get_premium_features()

                            if full_details:
                                return temp_subscription
                            return True
                    except Exception as free_day_error:
                        logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

                    if full_details:
                        return subscription_data
                    return False

            except Exception as e:
                logger.error(f"خطأ في التحقق من الاشتراك في Firestore للمستخدم {user_id}: {str(e)}")
                # إعادة قيم افتراضية في حالة الخطأ
                default_data = {
                    'is_active': False,
                    'expiry': None,
                    'features': self.get_free_features()
                }
                # تخزين في الذاكرة المحلية
                self._subscription_cache[user_id] = default_data
                self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

                # التحقق من اليوم المجاني
                try:
                    # استخدام الدالة الجديدة is_free_day_active
                    if free_day_system.is_free_day_active(user_id):
                        # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                        temp_subscription = default_data.copy()
                        temp_subscription['is_active'] = True
                        temp_subscription['is_free_day'] = True
                        temp_subscription['features'] = self.get_premium_features()

                        if full_details:
                            return temp_subscription
                        return True
                except Exception as free_day_error:
                    logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

                if full_details:
                    return default_data
                return False

        except Exception as e:
            logger.error(f"خطأ عام في التحقق من الاشتراك للمستخدم {user_id}: {str(e)}")
            # إعادة قيم افتراضية في حالة الخطأ
            default_data = {
                'is_active': False,
                'expiry': None,
                'features': self.get_free_features()
            }

            # التحقق من اليوم المجاني
            try:
                # استخدام الدالة الجديدة is_free_day_active
                if free_day_system.is_free_day_active(user_id):
                    # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                    temp_subscription = default_data.copy()
                    temp_subscription['is_active'] = True
                    temp_subscription['is_free_day'] = True
                    temp_subscription['features'] = self.get_premium_features()

                    if full_details:
                        return temp_subscription
                    return True
            except Exception as free_day_error:
                logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

            if full_details:
                return default_data
            return False

    # واجهات متوافقة مع الكود القديم
    def is_subscribed(self, user_id: str) -> bool:
        """التحقق من حالة اشتراك المستخدم"""
        return self.get_subscription_status(user_id, full_details=False)

    def get_subscription_details(self, user_id: str) -> dict:
        """الحصول على تفاصيل الاشتراك"""
        return self.get_subscription_status(user_id, full_details=True)

    def get_free_features(self) -> dict:
        """الحصول على المميزات المجانية"""
        return {
            'analyses_per_day': 3,
            'alerts': 1,
            'indicators': ['RSI', 'EMA'],
            'update_interval': 60
        }

    def get_premium_features(self) -> dict:
        """الحصول على المميزات المميزة"""
        return {
            'analyses_per_day': -1,  # غير محدود
            'alerts': -1,            # غير محدود
            'indicators': 'all',
            'update_interval': 1,
            'custom_indicators': True,
            'portfolio_management': True
        }

    def get_user_features(self, user_id: str) -> dict:
        """الحصول على مميزات المستخدم"""
        return self.get_premium_features() if self.is_subscribed(user_id) else self.get_free_features()

    # متغيرات لتخزين الاستخدام اليومي في الذاكرة المحلية
    _daily_usage_cache = {}
    _daily_usage_expiry = {}

    def get_daily_usage(self, user_id: str) -> dict:
        """الحصول على الاستخدام اليومي"""
        try:
            # التحقق من الذاكرة المحلية
            current_time = datetime.now()
            if user_id in self._daily_usage_cache and user_id in self._daily_usage_expiry:
                if current_time < self._daily_usage_expiry[user_id]:
                    return self._daily_usage_cache[user_id]

            # جلب البيانات من Firestore
            usage_ref = db.collection('daily_usage').document(user_id)
            usage_data = usage_ref.get()

            today = current_time.date().isoformat()

            if usage_data.exists:
                usage = usage_data.to_dict()
                # إعادة تعيين العداد إذا كان من يوم سابق
                if usage.get('date') != today:
                    usage = {
                        'date': today,
                        'analyses': 0,
                        'alerts': 0
                    }
            else:
                usage = {
                    'date': today,
                    'analyses': 0,
                    'alerts': 0
                }

            # تحديث Firestore
            usage_ref.set(usage)

            # تخزين في الذاكرة المحلية
            self._daily_usage_cache[user_id] = usage
            self._daily_usage_expiry[user_id] = current_time + timedelta(hours=1)

            return usage
        except Exception as e:
            logger.error(f"Error getting daily usage: {str(e)}")
            # في حالة حدوث خطأ، نعيد القيم الافتراضية
            today = datetime.now().date().isoformat()
            return {
                'date': today,
                'analyses': 0,
                'alerts': 0
            }

    # متغيرات لتخزين إعدادات المستخدم في الذاكرة المحلية
    _settings_cache = {}
    _settings_expiry = {}

    def get_user_settings(self, user_id: str) -> dict:
        """الحصول على إعدادات المستخدم"""
        try:
            # التحقق من الذاكرة المحلية
            current_time = datetime.now()

            # تحقق من وجود متغيرات الذاكرة المحلية
            if hasattr(self, '_settings_cache') and hasattr(self, '_settings_expiry'):
                if user_id in self._settings_cache and user_id in self._settings_expiry:
                    if current_time < self._settings_expiry[user_id]:
                        # استخدام البيانات من الذاكرة المحلية بدون تسجيل
                        return self._settings_cache[user_id]
            else:
                # تهيئة متغيرات الذاكرة المحلية إذا لم تكن موجودة
                self._settings_cache = {}
                self._settings_expiry = {}

            try:
                # جلب الإعدادات من Firestore
                settings_ref = db.collection('user_settings').document(user_id)
                settings_data = settings_ref.get()

                if settings_data.exists:
                    settings = settings_data.to_dict()
                else:
                    # إعدادات افتراضية للمستخدم الجديد
                    settings = {
                        'indicators': [],
                        'currencies': [],
                        'lang': 'ar'
                    }

                    try:
                        settings_ref.set(settings)
                    except Exception as e:
                        logger.error(f"خطأ في إنشاء إعدادات افتراضية للمستخدم {user_id} في Firestore: {str(e)}")

                # التأكد من وجود جميع المفاتيح الضرورية
                if 'lang' not in settings:
                    settings['lang'] = 'ar'
                if 'indicators' not in settings:
                    settings['indicators'] = []
                if 'currencies' not in settings:
                    settings['currencies'] = []

                # تخزين في الذاكرة المحلية
                self._settings_cache[user_id] = settings
                self._settings_expiry[user_id] = current_time + timedelta(hours=1)

                return settings

            except Exception as e:
                logger.error(f"خطأ في جلب إعدادات المستخدم {user_id} من Firestore: {str(e)}")
                # إعادة إعدادات افتراضية في حالة الخطأ
                default_settings = {'indicators': [], 'currencies': [], 'lang': 'ar'}
                return default_settings

        except Exception as e:
            logger.error(f"خطأ عام في الحصول على إعدادات المستخدم {user_id}: {str(e)}")
            # إعادة إعدادات افتراضية في حالة الخطأ
            default_settings = {'indicators': [], 'currencies': [], 'lang': 'ar'}
            return default_settings

    def update_user_settings(self, user_id: str, **settings) -> bool:
        """تحديث إعدادات المستخدم"""
        try:
            logger.info(f"جاري تحديث إعدادات المستخدم {user_id}: {settings}")

            try:
                # جلب الإعدادات الحالية من Firestore
                settings_ref = db.collection('user_settings').document(user_id)
                current_settings_data = settings_ref.get()

                if current_settings_data.exists:
                    logger.info(f"تم العثور على إعدادات المستخدم {user_id} في Firestore")
                    updated_settings = current_settings_data.to_dict()
                    updated_settings.update(settings)
                else:
                    logger.info(f"لم يتم العثور على إعدادات للمستخدم {user_id}، جاري إنشاء إعدادات جديدة")
                    # إنشاء إعدادات جديدة مع القيم الافتراضية
                    updated_settings = {
                        'indicators': [],
                        'currencies': [],
                        'lang': 'ar'
                    }
                    updated_settings.update(settings)

                # تحديث Firestore
                settings_ref.set(updated_settings)
                logger.info(f"تم تحديث إعدادات المستخدم {user_id} في Firestore")

                # تهيئة متغيرات الذاكرة المحلية إذا لم تكن موجودة
                if not hasattr(self, '_settings_cache'):
                    self._settings_cache = {}
                if not hasattr(self, '_settings_expiry'):
                    self._settings_expiry = {}

                # تحديث الذاكرة المحلية
                current_time = datetime.now()
                self._settings_cache[user_id] = updated_settings
                self._settings_expiry[user_id] = current_time + timedelta(hours=1)
                logger.info(f"تم تحديث إعدادات المستخدم {user_id} في الذاكرة المحلية")

                return True

            except Exception as e:
                logger.error(f"خطأ في تحديث إعدادات المستخدم {user_id} في Firestore: {str(e)}")
                return False

        except Exception as e:
            logger.error(f"خطأ عام في تحديث إعدادات المستخدم {user_id}: {str(e)}")
            return False

    # متغيرات لتخزين الاستخدام المجاني في الذاكرة المحلية
    _free_usage_cache = {}
    _free_usage_expiry = {}

    def get_free_usage(self, user_id: str) -> dict:
        """الحصول على الاستخدام المجاني المتبقي"""
        try:
            # التحقق من الذاكرة المحلية
            current_time = datetime.now()
            today = current_time.date().isoformat()

            # تحقق من وجود البيانات في الذاكرة المحلية
            if hasattr(self, '_free_usage_cache') and hasattr(self, '_free_usage_expiry'):
                if user_id in self._free_usage_cache and user_id in self._free_usage_expiry:
                    if current_time < self._free_usage_expiry[user_id]:
                        usage = self._free_usage_cache[user_id]

                        # إذا كان التاريخ مختلف، نقوم بإعادة تعيين العدادات
                        if usage.get('date') != today:
                            usage = {
                                'date': today,
                                'analyses': 3,
                                'alerts': 1
                            }
                            # تحديث الذاكرة المحلية
                            self._free_usage_cache[user_id] = usage
                            self._free_usage_expiry[user_id] = current_time + timedelta(hours=1)

                            try:
                                # تحديث Firestore
                                usage_ref = db.collection('free_usage').document(user_id)
                                usage_ref.set(usage)
                            except Exception as e:
                                logger.error(f"خطأ في تحديث بيانات الاستخدام المجاني في Firestore للمستخدم {user_id}: {str(e)}")

                        return usage

            try:
                # جلب البيانات من Firestore
                usage_ref = db.collection('free_usage').document(user_id)
                usage_data = usage_ref.get()

                if usage_data.exists:
                    usage = usage_data.to_dict()

                    # إذا كان التاريخ مختلف، نقوم بإعادة تعيين العدادات
                    if usage.get('date') != today:
                        usage = {
                            'date': today,
                            'analyses': 3,
                            'alerts': 1
                        }
                else:
                    # إنشاء بيانات جديدة
                    usage = {
                        'date': today,
                        'analyses': 3,
                        'alerts': 1
                    }

                # تحديث Firestore
                usage_ref.set(usage)

                # تهيئة متغيرات الذاكرة المحلية إذا لم تكن موجودة
                if not hasattr(self, '_free_usage_cache'):
                    self._free_usage_cache = {}
                if not hasattr(self, '_free_usage_expiry'):
                    self._free_usage_expiry = {}

                # تخزين في الذاكرة المحلية
                self._free_usage_cache[user_id] = usage
                self._free_usage_expiry[user_id] = current_time + timedelta(hours=1)

                return usage

            except Exception as e:
                logger.error(f"خطأ في جلب بيانات الاستخدام المجاني من Firestore للمستخدم {user_id}: {str(e)}")
                # في حالة حدوث خطأ، نعيد القيم الافتراضية
                default_usage = {
                    'date': today,
                    'analyses': 3,
                    'alerts': 1
                }
                return default_usage

        except Exception as e:
            logger.error(f"خطأ عام في الحصول على الاستخدام المجاني للمستخدم {user_id}: {str(e)}")
            # في حالة حدوث خطأ، نعيد القيم الافتراضية
            default_usage = {
                'date': datetime.now().date().isoformat(),
                'analyses': 3,
                'alerts': 1
            }
            return default_usage

    def use_free_analysis(self, user_id: str) -> bool:
        """استخدام تحليل مجاني"""
        try:
            logger.info(f"جاري استخدام تحليل مجاني للمستخدم {user_id}...")
            usage = self.get_free_usage(user_id)

            if usage['analyses'] > 0:
                logger.info(f"المستخدم {user_id} لديه {usage['analyses']} تحليلات متبقية، جاري استخدام واحد...")
                usage['analyses'] -= 1

                try:
                    # تحديث Firestore
                    usage_ref = db.collection('free_usage').document(user_id)
                    usage_ref.set(usage)
                    logger.info(f"تم تحديث بيانات الاستخدام المجاني في Firestore للمستخدم {user_id}")
                except Exception as e:
                    logger.error(f"خطأ في تحديث بيانات الاستخدام المجاني في Firestore للمستخدم {user_id}: {str(e)}")

                try:
                    # تهيئة متغيرات الذاكرة المحلية إذا لم تكن موجودة
                    if not hasattr(self, '_free_usage_cache'):
                        self._free_usage_cache = {}
                    if not hasattr(self, '_free_usage_expiry'):
                        self._free_usage_expiry = {}

                    # تحديث الذاكرة المحلية
                    current_time = datetime.now()
                    self._free_usage_cache[user_id] = usage
                    self._free_usage_expiry[user_id] = current_time + timedelta(hours=1)
                    logger.info(f"تم تحديث بيانات الاستخدام المجاني في الذاكرة المحلية للمستخدم {user_id}")
                except Exception as e:
                    logger.error(f"خطأ في تحديث بيانات الاستخدام المجاني في الذاكرة المحلية للمستخدم {user_id}: {str(e)}")

                return True
            else:
                logger.info(f"المستخدم {user_id} ليس لديه تحليلات مجانية متبقية")
                return False

        except Exception as e:
            logger.error(f"خطأ عام في استخدام تحليل مجاني للمستخدم {user_id}: {str(e)}")
            return False


    def use_free_alert(self, user_id: str) -> bool:
        """استخدام تنبيه مجاني"""
        try:
            logger.info(f"جاري التحقق من إمكانية استخدام تنبيه مجاني للمستخدم {user_id}...")

            # التحقق من حالة الاشتراك
            try:
                if self.is_subscribed(user_id):
                    logger.info(f"المستخدم {user_id} مشترك، يمكنه استخدام تنبيهات غير محدودة")
                    return True
            except Exception as e:
                logger.error(f"خطأ في التحقق من حالة اشتراك المستخدم {user_id}: {str(e)}")
                # نستمر في التنفيذ ونفترض أن المستخدم غير مشترك

            # الحصول على بيانات الاستخدام المجاني
            usage = self.get_free_usage(user_id)

            if usage['alerts'] > 0:
                logger.info(f"المستخدم {user_id} لديه {usage['alerts']} تنبيهات متبقية، جاري استخدام واحد...")
                usage['alerts'] -= 1

                try:
                    # تحديث Firestore
                    usage_ref = db.collection('free_usage').document(user_id)
                    usage_ref.set(usage)
                    logger.info(f"تم تحديث بيانات الاستخدام المجاني في Firestore للمستخدم {user_id}")
                except Exception as e:
                    logger.error(f"خطأ في تحديث بيانات الاستخدام المجاني في Firestore للمستخدم {user_id}: {str(e)}")

                try:
                    # تهيئة متغيرات الذاكرة المحلية إذا لم تكن موجودة
                    if not hasattr(self, '_free_usage_cache'):
                        self._free_usage_cache = {}
                    if not hasattr(self, '_free_usage_expiry'):
                        self._free_usage_expiry = {}

                    # تحديث الذاكرة المحلية
                    current_time = datetime.now()
                    self._free_usage_cache[user_id] = usage
                    self._free_usage_expiry[user_id] = current_time + timedelta(hours=1)
                    logger.info(f"تم تحديث بيانات الاستخدام المجاني في الذاكرة المحلية للمستخدم {user_id}")
                except Exception as e:
                    logger.error(f"خطأ في تحديث بيانات الاستخدام المجاني في الذاكرة المحلية للمستخدم {user_id}: {str(e)}")

                return True
            else:
                logger.info(f"المستخدم {user_id} ليس لديه تنبيهات مجانية متبقية")
                return False

        except Exception as e:
            logger.error(f"خطأ عام في استخدام تنبيه مجاني للمستخدم {user_id}: {str(e)}")
            return False

    async def activate_subscription(self, user_id: str, transaction_id: str, lang: str = 'ar') -> bool:
        """دالة موحدة لتفعيل الاشتراك"""
        try:
            # حساب تاريخ انتهاء الاشتراك
            current_time = datetime.now()
            expiry_date = current_time + timedelta(days=7)

            # إنشاء بيانات الاشتراك
            subscription_data = {
                'status': 'active',
                'subscription_date': current_time.isoformat(),
                'expiry': expiry_date.isoformat(),
                'transaction_id': transaction_id,
                'lang': lang,
                'features': self.get_premium_features()
            }

            # تحديث في جدول subscriptions
            user_ref = db.collection('subscriptions').document(user_id)
            user_ref.set(subscription_data)

            # تحديث في جدول users
            users_ref = db.collection('users').document(user_id)
            users_ref.update({
                'subscriptionStatus': 'مشترك',
                'subscriptionExpiry': expiry_date.isoformat(),
                'lastUpdated': current_time.isoformat()
            })

            # تحديث الذاكرة المحلية
            cache_data = {
                'is_active': True,
                'expiry': expiry_date.isoformat(),
                'features': self.get_premium_features()
            }
            self._subscription_cache[user_id] = cache_data
            self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

            # إعادة تعيين الاستخدام اليومي
            usage = {
                'date': current_time.date().isoformat(),
                'analyses': 0,
                'alerts': 0
            }
            usage_ref = db.collection('daily_usage').document(user_id)
            usage_ref.set(usage)

            # تخزين الاستخدام اليومي في الذاكرة المحلية
            self._daily_usage_cache = self._daily_usage_cache if hasattr(self, '_daily_usage_cache') else {}
            self._daily_usage_expiry = self._daily_usage_expiry if hasattr(self, '_daily_usage_expiry') else {}
            self._daily_usage_cache[user_id] = usage
            self._daily_usage_expiry[user_id] = current_time + timedelta(hours=1)

            logger.info(f"تم تفعيل الاشتراك للمستخدم {user_id} حتى {expiry_date}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تفعيل الاشتراك للمستخدم {user_id}: {str(e)}")
            return False

    def add_subscription(self, user_id: str, lang: str = 'ar', transaction_id: str = None) -> bool:
        """إضافة اشتراك جديد (واجهة متوافقة مع الكود القديم)"""
        try:
            current_time = datetime.now()
            expiry_date = current_time + timedelta(days=7)

            subscription_data = {
                'status': 'active',
                'subscription_date': current_time.isoformat(),
                'expiry': expiry_date.isoformat(),
                'transaction_id': transaction_id,
                'lang': lang,
                'features': self.get_premium_features()
            }

            # حفظ في Firestore
            user_ref = db.collection('subscriptions').document(user_id)
            user_ref.set(subscription_data)

            # تحديث الذاكرة المحلية
            cache_data = {
                'is_active': True,
                'expiry': expiry_date.isoformat(),
                'features': self.get_premium_features()
            }
            self._subscription_cache[user_id] = cache_data
            self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

            # إعادة تعيين الاستخدام اليومي
            usage = {
                'date': datetime.now().date().isoformat(),
                'analyses': 0,
                'alerts': 0
            }
            usage_ref = db.collection('daily_usage').document(user_id)
            usage_ref.set(usage)

            # تخزين الاستخدام اليومي في الذاكرة المحلية
            self._daily_usage_cache = self._daily_usage_cache if hasattr(self, '_daily_usage_cache') else {}
            self._daily_usage_expiry = self._daily_usage_expiry if hasattr(self, '_daily_usage_expiry') else {}
            self._daily_usage_cache[user_id] = usage
            self._daily_usage_expiry[user_id] = current_time + timedelta(hours=1)

            return True
        except Exception as e:
            logger.error(f"Error adding subscription: {str(e)}")
            return False

    def clear_user_cache(self, user_id: str) -> bool:
        """مسح الذاكرة المحلية للمستخدم"""
        try:
            # مسح جميع البيانات المحلية المتعلقة بالمستخدم
            # مسح بيانات الاشتراك
            if user_id in self._subscription_cache:
                del self._subscription_cache[user_id]
            if user_id in self._subscription_cache_expiry:
                del self._subscription_cache_expiry[user_id]

            # مسح بيانات الاستخدام اليومي
            if hasattr(self, '_daily_usage_cache') and user_id in self._daily_usage_cache:
                del self._daily_usage_cache[user_id]
            if hasattr(self, '_daily_usage_expiry') and user_id in self._daily_usage_expiry:
                del self._daily_usage_expiry[user_id]

            # مسح بيانات الاستخدام المجاني
            if hasattr(self, '_free_usage_cache') and user_id in self._free_usage_cache:
                del self._free_usage_cache[user_id]
            if hasattr(self, '_free_usage_expiry') and user_id in self._free_usage_expiry:
                del self._free_usage_expiry[user_id]

            # مسح إعدادات المستخدم
            if hasattr(self, '_settings_cache') and user_id in self._settings_cache:
                del self._settings_cache[user_id]
            if hasattr(self, '_settings_expiry') and user_id in self._settings_expiry:
                del self._settings_expiry[user_id]

            logger.info(f"Local cache cleared for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing local cache for user {user_id}: {str(e)}")
            return False

    async def reset_user_data(self, update: Update, context: CallbackContext):
        """إعادة تعيين بيانات المستخدم (للمطور فقط)"""
        try:
            # التحقق من أن المستخدم هو المطور
            if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
                await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
                logger.warning(f"محاولة غير مصرح بها لإعادة تعيين البيانات من قبل المستخدم {update.effective_user.id}")
                return

            # التحقق من وجود معرف المستخدم في الأمر
            if not context.args or len(context.args) == 0:
                # إذا لم يتم تحديد معرف، نستخدم معرف المطور نفسه
                target_user_id = str(update.effective_user.id)
                is_self_reset = True
            else:
                # الحصول على معرف المستخدم من الأمر
                target_user_id = context.args[0]
                is_self_reset = (target_user_id == str(update.effective_user.id))

            # مسح البيانات من Firestore
            collections_to_clear = ['free_usage', 'daily_usage', 'user_settings']
            for collection in collections_to_clear:
                doc_ref = db.collection(collection).document(target_user_id)
                doc_ref.delete()

            # مسح الذاكرة المؤقتة
            self.clear_user_cache(target_user_id)

            # إعادة تهيئة البيانات الافتراضية
            today = datetime.now().date().isoformat()
            default_usage = {
                'date': today,
                'analyses': 3,
                'alerts': 1
            }

            # حفظ البيانات الافتراضية الجديدة
            usage_ref = db.collection('free_usage').document(target_user_id)
            usage_ref.set(default_usage)

            # تحديث الذاكرة المحلية بالبيانات الجديدة
            current_time = datetime.now()
            self._free_usage_cache[target_user_id] = default_usage
            self._free_usage_expiry[target_user_id] = current_time + timedelta(hours=1)

            # إذا كان المطور يعيد تعيين بياناته الخاصة، نقوم بتعديل حالة الاشتراك مؤقتًا
            if is_self_reset:
                # تحديث حالة الاشتراك في جدول users
                users_ref = db.collection('users').document(target_user_id)
                users_ref.update({
                    'subscriptionStatus': 'غير مشترك',
                    'subscriptionExpiry': None,
                    'lastUpdated': datetime.now().isoformat()
                })

                # تحديث في جدول subscriptions
                subscription_ref = db.collection('subscriptions').document(target_user_id)
                subscription_data = {
                    'is_active': False,
                    'expiry': None,
                    'features': self.get_free_features()
                }
                subscription_ref.set(subscription_data)

                # تحديث الذاكرة المحلية
                self._subscription_cache[target_user_id] = subscription_data
                self._subscription_cache_expiry[target_user_id] = current_time + timedelta(hours=1)

                await update.message.reply_text(
                    "✅ تم إعادة تعيين بياناتك بنجاح\n"
                    "🔄 تم تحديث حالة الاشتراك إلى 'غير مشترك' مؤقتًا\n"
                    "🔄 تم تحديث عدد المحاولات المجانية:\n"
                    "📊 تحليلات: 3\n"
                    "🔔 تنبيهات: 1\n\n"
                    "ملاحظة: يمكنك استخدام الأمر /set_subscription me active لاستعادة حالة الاشتراك"
                )
            else:
                await update.message.reply_text(
                    f"✅ تم إعادة تعيين بيانات المستخدم {target_user_id} بنجاح\n"
                    "🔄 تم تحديث عدد المحاولات المجانية:\n"
                    "📊 تحليلات: 3\n"
                    "🔔 تنبيهات: 1"
                )

            # تسجيل العملية
            logger.info(f"تم إعادة تعيين بيانات المستخدم {target_user_id} بنجاح")

        except Exception as e:
            logger.error(f"خطأ في إعادة تعيين بيانات المستخدم: {str(e)}", exc_info=False)
            await update.message.reply_text("❌ حدث خطأ أثناء إعادة تعيين البيانات")

    async def set_subscription_status(self, update: Update, context: CallbackContext):
        """تغيير حالة الاشتراك للمستخدمين (للمطور فقط)"""
        try:
            # التحقق من أن المستخدم هو المطور
            if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
                await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
                return

            # التحقق من وجود المعرف والحالة في الأوامر
            if len(context.args) != 2:
                await update.message.reply_text("❌ الرجاء إدخال المعرف والحالة (active/inactive)\nمثال: /set_subscription me active")
                return

            user_id, status = context.args

            # إذا كان المعرف هو "me"، استخدم معرف المطور نفسه
            if user_id.lower() == 'me':
                user_id = str(update.effective_user.id)

            # تحويل الحالة إلى الصيغة المناسبة
            if status.lower() in ['active', 'مشترك']:
                status = 'مشترك'
                is_active = True
            elif status.lower() in ['inactive', 'غير مشترك']:
                status = 'غير مشترك'
                is_active = False
            else:
                await update.message.reply_text("❌ الحالة غير صحيحة. الرجاء استخدام 'active' أو 'inactive'")
                return

            # تحديث حالة الاشتراك في جدول users
            users_ref = db.collection('users').document(user_id)

            # حساب تاريخ انتهاء الاشتراك (أسبوع من الآن)
            expiry_date = (datetime.now() + timedelta(days=7)).isoformat() if is_active else None

            # تحديث بيانات المستخدم
            user_data = {
                'subscriptionStatus': status,
                'subscriptionExpiry': expiry_date,
                'lastUpdated': datetime.now().isoformat()
            }
            users_ref.set(user_data, merge=True)

            # تحديث في جدول subscriptions أيضاً
            subscription_data = {
                'is_active': is_active,
                'expiry': expiry_date,
                'features': self.get_premium_features() if is_active else self.get_free_features()
            }

            if is_active:
                subscription_data.update({
                    'status': 'active',
                    'subscription_date': datetime.now().isoformat(),
                })
            else:
                subscription_data.update({
                    'status': 'inactive',
                })

            db.collection('subscriptions').document(user_id).set(subscription_data)

            # مسح الذاكرة المؤقتة للمستخدم
            self.clear_user_cache(user_id)

            # إرسال رسالة تأكيد
            await update.message.reply_text(
                f"✅ تم تحديث حالة المستخدم {user_id} إلى {status}\n"
                f"📅 تاريخ الانتهاء: {expiry_date if is_active else 'غير محدد'}"
            )

            logger.info(f"تم تحديث حالة المستخدم {user_id} إلى {status}")

        except Exception as e:
            logger.error(f"خطأ في تغيير حالة الاشتراك: {str(e)}")
            await update.message.reply_text("❌ حدث خطأ أثناء تغيير حالة الاشتراك")

    async def manage_currencies(self, update: Update, context: CallbackContext):
        """إدارة العملات المخصصة"""
        try:
            user_id = str(update.effective_user.id)
            settings = self.get_user_settings(user_id)
            lang = settings.get('lang', 'ar')

            # التحقق من الاشتراك
            if not self.is_subscribed(user_id):
                if hasattr(update, 'callback_query') and update.callback_query:
                    await update.callback_query.answer(
                        "عذراً، هذه الميزة متوفرة فقط للمشتركين" if lang == 'ar' else
                        "Sorry, this feature is only available for subscribers",
                        show_alert=True
                    )
                else:
                    await update.message.reply_text(
                        "عذراً، هذه الميزة متوفرة فقط للمشتركين" if lang == 'ar' else
                        "Sorry, this feature is only available for subscribers"
                    )
                return

            # جلب العملات المخصصة
            custom_currencies = settings.get('currencies', [])

            # إنشاء قائمة العملات
            currencies_text = (
                "🔍 العملات المخصصة:" if lang == 'ar' else "🔍 Custom Currencies:"
            )

            if custom_currencies:
                for currency in custom_currencies:
                    currencies_text += f"\n• {currency}"
            else:
                currencies_text += (
                    "\nلا توجد عملات مخصصة" if lang == 'ar' else
                    "\nNo custom currencies"
                )

            # إنشاء الأزرار
            keyboard = [
                [
                    InlineKeyboardButton(
                        "➕ إضافة عملة" if lang == 'ar' else "➕ Add Currency",
                        callback_data='add_currency'
                    )
                ]
            ]

            if custom_currencies:
                keyboard.append([
                    InlineKeyboardButton(
                        "➖ حذف عملة" if lang == 'ar' else "➖ Remove Currency",
                        callback_data='remove_currency'
                    )
                ])

            keyboard.append([
                InlineKeyboardButton(
                    "🔙 رجوع" if lang == 'ar' else "🔙 Back",
                    callback_data='back_to_main'
                )
            ])

            # التحقق من نوع التحديث (رسالة أو نقرة زر)
            if hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.edit_message_text(
                    text=currencies_text,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
            else:
                await update.message.reply_text(
                    text=currencies_text,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )

        except Exception as e:
            logger.error(f"Error managing currencies: {str(e)}")
            try:
                if hasattr(update, 'callback_query') and update.callback_query:
                    await update.callback_query.answer(
                        "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
                        "Sorry, an error occurred. Please try again"
                    )
                else:
                    await update.message.reply_text(
                        "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
                        "Sorry, an error occurred. Please try again"
                    )
            except Exception as inner_e:
                logger.error(f"Error sending error message: {str(inner_e)}")

    async def add_currency(self, update: Update, context: CallbackContext):
        """إضافة عملة جديدة"""
        try:
            user_id = str(update.effective_user.id)
            settings = self.get_user_settings(user_id)
            lang = settings.get('lang', 'ar')

            # التحقق من الاشتراك
            if not self.is_subscribed(user_id):
                await update.callback_query.answer(
                    "عذراً، هذه الميزة متوفرة فقط للمشتركين" if lang == 'ar' else
                    "Sorry, this feature is only available for subscribers",
                    show_alert=True
                )
                return

            # إنشاء قائمة العملات المتاحة مع إضافة الأعلام والريال السعودي
            available_currencies = [
                ('🇺🇸 USD', 'USD'),
                ('🇪🇺 EUR', 'EUR'),
                ('🇬🇧 GBP', 'GBP'),
                ('🇯🇵 JPY', 'JPY'),
                ('🇦🇺 AUD', 'AUD'),
                ('🇨🇦 CAD', 'CAD'),
                ('🇨🇭 CHF', 'CHF'),
                ('🇸🇦 SAR', 'SAR')
            ]
            keyboard = []

            for display_name, currency_code in available_currencies:
                keyboard.append([
                    InlineKeyboardButton(
                        display_name,
                        callback_data=f'add_currency_{currency_code}'
                    )
                ])

            keyboard.append([
                InlineKeyboardButton(
                    "🔙 رجوع" if lang == 'ar' else "🔙 Back",
                    callback_data='manage_currencies'
                )
            ])

            await update.callback_query.edit_message_text(
                text=get_text('select_currency_add', lang),
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        except Exception as e:
            logger.error(f"Error adding currency: {str(e)}")
            await update.callback_query.answer(
                "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
                "Sorry, an error occurred. Please try again"
            )

    async def remove_currency(self, update: Update, context: CallbackContext):
        """حذف عملة"""
        try:
            user_id = str(update.effective_user.id)
            settings = self.get_user_settings(user_id)
            lang = settings.get('lang', 'ar')
            custom_currencies = settings.get('currencies', [])

            if not custom_currencies:
                await update.callback_query.answer(
                    "لا توجد عملات مخصصة لحذفها" if lang == 'ar' else
                    "No custom currencies to remove",
                    show_alert=True
                )
                return

            keyboard = []
            # قاموس لربط رموز العملات بأعلامها
            currency_flags = {
                'USD': '🇺🇸',
                'EUR': '🇪🇺',
                'GBP': '🇬🇧',
                'JPY': '🇯🇵',
                'AUD': '🇦🇺',
                'CAD': '🇨🇦',
                'CHF': '🇨🇭',
                'SAR': '🇸🇦'
            }

            for currency in custom_currencies:
                # إضافة العلم إذا كان متوفرًا للعملة
                flag = currency_flags.get(currency, '')
                display_name = f"{flag} {currency}" if flag else currency

                keyboard.append([
                    InlineKeyboardButton(
                        display_name,
                        callback_data=f'remove_currency_{currency}'
                    )
                ])

            keyboard.append([
                InlineKeyboardButton(
                    "🔙 رجوع" if lang == 'ar' else "🔙 Back",
                    callback_data='manage_currencies'
                )
            ])

            await update.callback_query.edit_message_text(
                text=get_text('select_currency_remove', lang),
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        except Exception as e:
            logger.error(f"Error removing currency: {str(e)}")
            await update.callback_query.answer(
                "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
                "Sorry, an error occurred. Please try again"
            )

# تهيئة النظام العالمي
subscription_system = SubscriptionSystem()

# Global state
user_states = {}
price_alerts = {}
user_settings = {}

# إعدادات البوت
BOT_CONFIG = {

    'owner_id': SystemConfig.DEVELOPER_ID,  # استخدام المعرف المركزي دائماً
    'backup_interval': int(os.getenv('BACKUP_INTERVAL', '24')),
    'stats_interval': int(os.getenv('STATS_INTERVAL', '12')),
}

# استيراد نظام التخزين المؤقت باستخدام Firestore
from utils.firestore_cache import FirestoreCache

# تهيئة نظام التخزين المؤقت
firestore_cache = FirestoreCache(db)

# تهيئة النظام
config = Config()

class CryptoAnalysis:
    def __init__(self):
        self.binance_api = "https://api.binance.com/api/v3"
        self.exchange_api = "https://api.exchangerate-api.com/v4/latest/USD"
        self.exchange_rates = self.get_exchange_rates()
        self.custom_indicators = {}  # تخزين المؤشرات المخصصة لكل مستخدم
        self.market_data_cache = {}  # ذاكرة محلية لتخزين بيانات السوق
        self.market_data_expiry = {}  # تواريخ انتهاء صلاحية البيانات المخزنة
        self.cache_timeout = 300  # 5 minutes

    def get_exchange_rates(self):
        """الحصول على أسعار صرف العملات"""
        try:
            response = requests.get(self.exchange_api, timeout=10)
            if response.status_code == 200:
                return response.json()['rates']
            return None
        except:
            return None

    def convert_price(self, price_usd, target_currency='USD'):
        """تحويل السعر من الدولار إلى العملة المطلوبة"""
        if target_currency == 'USD' or not self.exchange_rates:
            return price_usd, 'USD'

        try:
            rate = self.exchange_rates.get(target_currency)
            if rate:
                return price_usd * rate, target_currency
        except:
            pass

        return price_usd, 'USD'

    def calculate_ema(self, prices, period):
        return prices.ewm(span=period, adjust=False).mean()

    def calculate_macd(self, prices, fast=12, slow=26, signal=9):
        exp1 = prices.ewm(span=fast, adjust=False).mean()
        exp2 = prices.ewm(span=slow, adjust=False).mean()
        macd = exp1 - exp2
        signal_line = macd.ewm(span=signal, adjust=False).mean()
        histogram = macd - signal_line
        return macd.iloc[-1], signal_line.iloc[-1], histogram.iloc[-1]

    def calculate_bollinger_bands(self, prices, period=20, std=2):
        middle_band = prices.rolling(window=period).mean()
        std_dev = prices.rolling(window=period).std()
        upper_band = middle_band + (std_dev * std)
        lower_band = middle_band - (std_dev * std)
        return upper_band.iloc[-1], middle_band.iloc[-1], lower_band.iloc[-1]

    def calculate_ichimoku_cloud(self, high, low, close, conversion_period=9, base_period=26, span_period=52, displacement=26):
        """
        حساب مؤشر سحابة إيشيموكو (Ichimoku Cloud)

        Args:
            high: سلسلة القيم العليا
            low: سلسلة القيم الدنيا
            close: سلسلة قيم الإغلاق
            conversion_period: فترة خط التحويل (Tenkan-sen)
            base_period: فترة خط الأساس (Kijun-sen)
            span_period: فترة السبان B (Senkou Span B)
            displacement: فترة الإزاحة

        Returns:
            خمس قيم: خط التحويل، خط الأساس، السبان A، السبان B، خط التشيكو
        """
        # حساب خط التحويل (Tenkan-sen)
        tenkan_sen = (high.rolling(window=conversion_period).max() + low.rolling(window=conversion_period).min()) / 2

        # حساب خط الأساس (Kijun-sen)
        kijun_sen = (high.rolling(window=base_period).max() + low.rolling(window=base_period).min()) / 2

        # حساب خط السبان A (Senkou Span A)
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(displacement)

        # حساب خط السبان B (Senkou Span B)
        senkou_span_b = ((high.rolling(window=span_period).max() + low.rolling(window=span_period).min()) / 2).shift(displacement)

        # حساب خط التشيكو (Chikou Span)
        chikou_span = close.shift(-displacement)

        # إرجاع القيم الحالية
        return (
            tenkan_sen.iloc[-1] if not tenkan_sen.empty else float('nan'),
            kijun_sen.iloc[-1] if not kijun_sen.empty else float('nan'),
            senkou_span_a.iloc[-displacement] if len(senkou_span_a) > displacement else float('nan'),
            senkou_span_b.iloc[-displacement] if len(senkou_span_b) > displacement else float('nan'),
            chikou_span.iloc[-1] if not chikou_span.empty else float('nan')
        )

    def calculate_stoch_rsi(self, prices, period=14, smooth_k=3, smooth_d=3):
        # Calculate RSI
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Stochastic RSI
        stoch_rsi = (rsi - rsi.rolling(period).min()) / (rsi.rolling(period).max() - rsi.rolling(period).min())
        k = stoch_rsi.rolling(smooth_k).mean()
        d = k.rolling(smooth_d).mean()
        return k.iloc[-1], d.iloc[-1]

    def calculate_adx(self, high, low, close, period=14):
        # True Range
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(period).mean()

        # Plus Directional Movement
        up_move = high - high.shift()
        down_move = low.shift() - low
        plus_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
        plus_di = 100 * pd.Series(plus_dm).rolling(period).mean() / atr

        # Minus Directional Movement
        minus_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)
        minus_di = 100 * pd.Series(minus_dm).rolling(period).mean() / atr

        # ADX
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(period).mean()
        return adx.iloc[-1], plus_di.iloc[-1], minus_di.iloc[-1]

    def get_recommendation(self, market_data, lang='ar'):
        """الحصول على التوصية بناءً على تحليل المؤشرات"""
        try:
            signals = []
            recommendation = None

            # تحليل RSI
            if market_data['rsi'] > 70:
                signals.append(
                    "RSI indicates overbought - Consider selling" if lang == 'en' else
                    "مؤشر القوة النسبية يشير إلى تشبع شراء - فرصة للبيع"
                )
                recommendation = "Sell" if lang == 'en' else "بيع"
            elif market_data['rsi'] < 30:
                signals.append(
                    "RSI indicates oversold - Consider buying" if lang == 'en' else
                    "مؤشر القوة النسبية يشير إلى تشبع بيع - فرصة للشراء"
                )
                recommendation = "Buy" if lang == 'en' else "شراء"

            # تحليل MACD
            if market_data['macd'] > market_data['macd_signal']:
                signals.append(
                    "MACD above signal line - Bullish signal" if lang == 'en' else
                    "مؤشر الماكد فوق خط الإشارة - إشارة إيجابية"
                )
                if not recommendation:
                    recommendation = "Buy" if lang == 'en' else "شراء"
            elif market_data['macd'] < market_data['macd_signal']:
                signals.append(
                    "MACD below signal line - Bearish signal" if lang == 'en' else
                    "مؤشر الماكد تحت خط الإشارة - إشارة سلبية"
                )
                if not recommendation:
                    recommendation = "Sell" if lang == 'en' else "بيع"

            # تحليل EMA
            if market_data['ema20'] > market_data['ema50']:
                signals.append(
                    "EMA20 above EMA50 - Upward trend" if lang == 'en' else
                    "المتوسط المتحرك 20 فوق 50 - اتجاه صعودي"
                )
                if not recommendation:
                    recommendation = "Buy" if lang == 'en' else "شراء"
            elif market_data['ema20'] < market_data['ema50']:
                signals.append(
                    "EMA20 below EMA50 - Downward trend" if lang == 'en' else
                    "المتوسط المتحرك 20 تحت 50 - اتجاه هبوطي"
                )
                if not recommendation:
                    recommendation = "Sell" if lang == 'en' else "بيع"

            # إذا لم يتم تحديد توصية واضحة
            if not recommendation:
                recommendation = "Hold" if lang == 'en' else "انتظار"
                signals.append(
                    "Mixed signals - Consider holding" if lang == 'en' else
                    "إشارات متضاربة - يفضل الانتظار"
                )

            return {
                'signals': signals,
                'recommendation': recommendation
            }

        except Exception as e:
            logger.error(f"Error in get_recommendation: {str(e)}")
            return {
                'signals': [
                    "Error analyzing indicators" if lang == 'en' else
                    "خطأ في تحليل المؤشرات"
                ],
                'recommendation': "Hold" if lang == 'en' else "انتظار"
            }

    def create_chart(self, symbol: str, df: pd.DataFrame, indicators: dict) -> tuple:
        """
        إنشاء مخطط تفاعلي مع المؤشرات الفنية وإرجاعه كبيانات ثنائية في الذاكرة

        Returns:
            tuple: (binary_data, content_type) - البيانات الثنائية للصورة ونوع المحتوى
        """
        try:
            # تقليل عدد النقاط في المخطط لتحسين الأداء
            df = df.iloc[-50:]  # نأخذ آخر 50 نقطة فقط

            # إعداد الألوان والنمط
            mc = mpf.make_marketcolors(
                up='green',
                down='red',
                edge='inherit',
                wick='inherit',
                volume='in',
                ohlc='inherit'
            )
            s = mpf.make_mpf_style(
                marketcolors=mc,
                gridstyle='--',
                y_on_right=True
            )

            # تحويل المؤشرات إلى Series
            ema20_plot = pd.Series(indicators['ema20'], index=df.index)
            ema50_plot = pd.Series(indicators['ema50'], index=df.index)
            rsi_plot = pd.Series(indicators['rsi'], index=df.index)
            macd_plot = pd.Series([indicators['macd']] * len(df), index=df.index)
            signal_plot = pd.Series([indicators['macd_signal']] * len(df), index=df.index)
            histogram_plot = pd.Series([indicators['macd_histogram']] * len(df), index=df.index)
            bb_upper_plot = pd.Series([indicators['bb_upper']] * len(df), index=df.index)
            bb_lower_plot = pd.Series([indicators['bb_lower']] * len(df), index=df.index)

            # تحويل مؤشرات Ichimoku Cloud إلى Series (للمستخدمين المشتركين فقط)
            is_subscribed = indicators.get('is_subscribed', False)
            if is_subscribed:
                # تحويل قيم Ichimoku Cloud إلى Series
                ichimoku_tenkan_plot = pd.Series([indicators['ichimoku_tenkan']] * len(df), index=df.index)
                ichimoku_kijun_plot = pd.Series([indicators['ichimoku_kijun']] * len(df), index=df.index)

                # إنشاء Series لـ Senkou Span A و Senkou Span B (السحابة)
                ichimoku_senkou_a_plot = pd.Series([indicators['ichimoku_senkou_a']] * len(df), index=df.index)
                ichimoku_senkou_b_plot = pd.Series([indicators['ichimoku_senkou_b']] * len(df), index=df.index)

            # إضافة المؤشرات للمخطط
            apds = [
                mpf.make_addplot(ema20_plot, color='blue', width=0.7),
                mpf.make_addplot(ema50_plot, color='red', width=0.7),
                mpf.make_addplot(rsi_plot, panel=2, color='purple', width=0.7),
                mpf.make_addplot(pd.Series([30] * len(df), index=df.index), panel=2, color='g', linestyle='--'),
                mpf.make_addplot(pd.Series([70] * len(df), index=df.index), panel=2, color='r', linestyle='--'),
                mpf.make_addplot(macd_plot, panel=3, color='blue', width=0.7),
                mpf.make_addplot(signal_plot, panel=3, color='red', width=0.7),
                mpf.make_addplot(histogram_plot, panel=3, type='bar', width=0.7, color='dimgray'),
                mpf.make_addplot(bb_upper_plot, color='gray', width=0.7),
                mpf.make_addplot(bb_lower_plot, color='gray', width=0.7)
            ]

            # إضافة مؤشرات Ichimoku Cloud للمستخدمين المشتركين فقط
            if is_subscribed:
                # تحقق من صحة بيانات Senkou Span A و B قبل إضافتها للمخطط
                senkou_a_nan = ichimoku_senkou_a_plot.isnull()
                senkou_b_nan = ichimoku_senkou_b_plot.isnull()
                leading_nan_a = senkou_a_nan.cumsum().where(~senkou_a_nan).ffill().fillna(0).max()
                leading_nan_b = senkou_b_nan.cumsum().where(~senkou_b_nan).ffill().fillna(0).max()
                valid_a = not senkou_a_nan[int(leading_nan_a):].any()
                valid_b = not senkou_b_nan[int(leading_nan_b):].any()
                equal_length = len(ichimoku_senkou_a_plot) == len(ichimoku_senkou_b_plot)

                if valid_a and valid_b and equal_length:
                    # إضافة خطوط Tenkan-sen و Kijun-sen
                    apds.extend([
                        mpf.make_addplot(ichimoku_tenkan_plot, color='green', width=0.7, linestyle='-'),
                        mpf.make_addplot(ichimoku_kijun_plot, color='red', width=0.7, linestyle='-'),
                        # إضافة خطوط Senkou Span A و Senkou Span B (السحابة)
                        mpf.make_addplot(ichimoku_senkou_a_plot, color='green', width=0.5, linestyle='--', alpha=0.5),
                        mpf.make_addplot(ichimoku_senkou_b_plot, color='red', width=0.5, linestyle='--', alpha=0.5)
                    ])

                    # إضافة منطقة السحابة (الفراغ بين Senkou Span A و Senkou Span B)
                    # تحديد اللون بناءً على أيهما أعلى
                    if indicators['ichimoku_senkou_a'] > indicators['ichimoku_senkou_b']:
                        # سحابة خضراء (صعودية)
                        apds.append(mpf.make_addplot(
                            ichimoku_senkou_a_plot,
                            color='green', width=0, alpha=0.2, panel=0, secondary_y=False,
                            fill_between=dict(y1=ichimoku_senkou_b_plot.values)
                        ))
            try:
                # استخدام BytesIO لحفظ الصورة في الذاكرة بدلاً من القرص
                from io import BytesIO
                buffer = BytesIO()

                logger.info(f"جاري إنشاء الرسم البياني في الذاكرة لـ {symbol}")

                # استخدام سياق لإغلاق الشكل بشكل صحيح
                with plt.rc_context():
                    fig, axes = mpf.plot(
                        df,
                        type='candle',
                        volume=True,
                        addplot=apds,
                        style=s,
                        title=f'\n{symbol} Technical Analysis',
                        panel_ratios=(6,2,2,2),
                        volume_panel=1,
                        returnfig=True,
                        figsize=(12, 10),
                        tight_layout=True
                    )

                    # حفظ المخطط في الذاكرة بدلاً من القرص
                    plt.savefig(buffer, dpi=300, bbox_inches='tight', format='png')
                    plt.close(fig)  # إغلاق الشكل بشكل صريح

                # تنظيف الذاكرة
                gc.collect()

                # التحقق من حجم البيانات
                buffer.seek(0)
                img_data = buffer.getvalue()

                if len(img_data) > 0:
                    logger.info(f"تم إنشاء الرسم البياني بنجاح في الذاكرة (حجم البيانات: {len(img_data)} بايت)")
                    return (img_data, 'image/png')
                else:
                    logger.error("البيانات فارغة، محاولة إنشاء رسم بياني بديل")
                    # محاولة إنشاء رسم بياني بسيط كبديل
                    backup_buffer = BytesIO()

                    plt.figure(figsize=(10, 6))
                    plt.plot(df['close'], label='Price')
                    plt.title(f"{symbol} Price Chart")
                    plt.legend()
                    plt.grid(True)
                    plt.savefig(backup_buffer, format='jpeg', dpi=150)
                    plt.close()

                    backup_buffer.seek(0)
                    backup_data = backup_buffer.getvalue()

                    if len(backup_data) > 0:
                        logger.info(f"تم إنشاء رسم بياني بديل في الذاكرة (حجم البيانات: {len(backup_data)} بايت)")
                        return (backup_data, 'image/jpeg')
                    else:
                        logger.error("فشل إنشاء الرسم البياني البديل")
                        return (None, None)

            except Exception as chart_error:
                logger.error(f"خطأ أثناء إنشاء الرسم البياني: {str(chart_error)}")
                # محاولة إنشاء رسم بياني بسيط جداً كملاذ أخير
                try:
                    fallback_buffer = BytesIO()

                    # إنشاء رسم بياني بسيط جداً
                    plt.figure(figsize=(8, 5))
                    plt.plot(df['close'].values)
                    plt.title(f"{symbol}")
                    plt.savefig(fallback_buffer, format='jpeg', dpi=100)
                    plt.close()

                    fallback_buffer.seek(0)
                    fallback_data = fallback_buffer.getvalue()

                    if len(fallback_data) > 0:
                        logger.info(f"تم إنشاء رسم بياني بسيط جداً في الذاكرة (حجم البيانات: {len(fallback_data)} بايت)")
                        return (fallback_data, 'image/jpeg')
                except Exception as fallback_error:
                    logger.error(f"فشل إنشاء الرسم البياني البسيط جداً: {str(fallback_error)}")

                return (None, None)

        except Exception as e:
            logger.error(f"Error creating chart: {str(e)}")
            return (None, None)

    async def get_market_data(self, symbol: str, target_currency='USD', user_id=None, lang='ar', interval='4h'):
        """
        الحصول على بيانات السوق مع استخدام الذاكرة المحلية

        Args:
            symbol: رمز العملة
            target_currency: العملة المستهدفة للتحويل
            user_id: معرف المستخدم
            lang: لغة التحليل
            interval: الإطار الزمني (1h, 4h, 1d, 1w)
        """
        try:
            # التحقق من صحة الرمز
            if not symbol or len(symbol) < 2:
                logger.error("Invalid symbol")
                return None

            # تنظيف وتحقق من رمز العملة
            def clean_symbol(symbol: str) -> str:
                symbol = symbol.upper().strip()
                # إذا كان الرمز لا ينتهي بـ USDT، أضفه
                if not symbol.endswith('USDT'):
                    symbol = f"{symbol}USDT"
                # إضافة تحقق إضافي من التنسيق - تم تعديله لتسمح برموز قصيرة مثل "BNB"
                if len(symbol) < 5 or not any(c.isalpha() for c in symbol[:-4]):
                    raise ValueError("رمز عملة غير صالح")
                return symbol

            symbol = clean_symbol(symbol)

            # التحقق من الذاكرة المحلية - إضافة الإطار الزمني للمفتاح
            cache_key = f"market_data_{symbol}_{interval}_{lang}"
            current_time = datetime.now()

            if cache_key in self.market_data_cache and cache_key in self.market_data_expiry:
                if current_time < self.market_data_expiry[cache_key]:
                    # استخدام البيانات المخزنة مع تحديث المخطط البياني فقط إذا لزم الأمر
                    cached_data = self.market_data_cache[cache_key]
                    # إذا كان هناك حاجة لإنشاء مخطط جديد (مثلاً إذا تم حذف المخطط السابق أو لم يكن موجوداً)
                    if 'chart_path' not in cached_data or not cached_data['chart_path'] or (cached_data['chart_path'] and not os.path.exists(cached_data['chart_path'])):
                        logger.info(f"إنشاء مخطط جديد للعملة {symbol} بإطار زمني {interval} لأن المخطط السابق غير موجود أو تم حذفه")
                        # استخدام BinanceAPIManager للحصول على البيانات الحديثة للمخطط فقط
                        klines = await binance_manager.get_klines(symbol, interval=interval, user_id=user_id)
                        if klines:
                            df = pd.DataFrame(klines, columns=[
                                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                                'taker_buy_quote', 'ignore'
                            ])
                            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'quote_volume']
                            df[numeric_columns] = df[numeric_columns].astype(float)
                            df['Date'] = pd.to_datetime(df['timestamp'], unit='ms')
                            df.set_index('Date', inplace=True)

                            # إضافة DataFrame إلى البيانات المخزنة مؤقتًا
                            cached_data['df'] = df

                            # إنشاء المخطط البياني فقط
                            indicators = {
                                'rsi': cached_data['rsi'],
                                'ema20': cached_data['ema20'],
                                'ema50': cached_data['ema50'],
                                'macd': cached_data['macd'],
                                'macd_signal': cached_data['macd_signal'],
                                'macd_histogram': cached_data['macd_histogram'],
                                'bb_upper': cached_data['bb_upper'],
                                'bb_middle': cached_data['bb_middle'],
                                'bb_lower': cached_data['bb_lower'],
                                'stoch_k': cached_data['stoch_k'],
                                'stoch_d': cached_data['stoch_d'],
                                'adx': cached_data['adx'],
                                'plus_di': cached_data['plus_di'],
                                'minus_di': cached_data['minus_di']
                            }
                            chart_data, content_type = self.create_chart(symbol, df, indicators)
                            if chart_data:
                                cached_data['chart_data'] = chart_data
                                cached_data['chart_content_type'] = content_type
                                logger.info(f"تم إنشاء مخطط جديد للعملة {symbol} بإطار زمني {interval} في الذاكرة (حجم البيانات: {len(chart_data)} بايت)")
                            else:
                                logger.error(f"فشل في إنشاء مخطط للعملة {symbol} بإطار زمني {interval}")

                    return cached_data

            # محاولة استخدام مفاتيح API الخاصة بالمستخدم إذا كان متاحًا
            if user_id and api_manager:
                try:
                    # التحقق من وجود مفاتيح API للمستخدم
                    has_api_keys = await api_manager.has_api_keys(user_id, 'binance')

                    if has_api_keys:
                        logger.info(f"استخدام مفاتيح API الخاصة بالمستخدم {user_id} للحصول على بيانات {symbol}")

                        # الحصول على مفاتيح API الخاصة بالمستخدم
                        api_key, api_secret = await api_manager.get_api_keys(user_id, 'binance')

                        if api_key and api_secret:
                            # استخدام مكتبة python-binance مع مفاتيح API المستخدم
                            from binance.client import Client
                            client = Client(api_key, api_secret)

                            # الحصول على بيانات السوق باستخدام API المستخدم
                            from analysis.user_market_data import get_market_data_with_user_api
                            user_market_data = await get_market_data_with_user_api(client, symbol, interval, 100, target_currency)

                            if user_market_data:
                                logger.info(f"تم الحصول على بيانات السوق للعملة {symbol} باستخدام API المستخدم {user_id}")

                                # التحقق من وجود بيانات السعر
                                if 'price' in user_market_data and user_market_data['price'] > 0:
                                    logger.info(f"السعر الحالي للعملة {symbol}: {user_market_data['price']}")
                                else:
                                    logger.warning(f"لم يتم العثور على سعر صالح للعملة {symbol}")

                                # إنشاء المخطط البياني إذا كان مطلوبًا
                                if 'df' in user_market_data and 'chart_path' not in user_market_data:
                                    try:
                                        # تجميع المؤشرات لإنشاء المخطط
                                        chart_indicators = {
                                            'rsi': user_market_data.get('rsi'),
                                            'ema20': user_market_data.get('ema20'),
                                            'ema50': user_market_data.get('ema50'),
                                            'macd': user_market_data.get('macd'),
                                            'macd_signal': user_market_data.get('macd_signal'),
                                            'macd_histogram': user_market_data.get('macd_histogram'),
                                            'bb_upper': user_market_data.get('bb_upper'),
                                            'bb_middle': user_market_data.get('bb_middle'),
                                            'bb_lower': user_market_data.get('bb_lower'),
                                            'stoch_k': user_market_data.get('stoch_k'),
                                            'stoch_d': user_market_data.get('stoch_d'),
                                            'adx': user_market_data.get('adx'),
                                            'plus_di': user_market_data.get('plus_di'),
                                            'minus_di': user_market_data.get('minus_di'),
                                            # إضافة مؤشرات Ichimoku Cloud
                                            'ichimoku_tenkan': user_market_data.get('ichimoku_tenkan', float('nan')),
                                            'ichimoku_kijun': user_market_data.get('ichimoku_kijun', float('nan')),
                                            'ichimoku_senkou_a': user_market_data.get('ichimoku_senkou_a', float('nan')),
                                            'ichimoku_senkou_b': user_market_data.get('ichimoku_senkou_b', float('nan')),
                                            'ichimoku_chikou': user_market_data.get('ichimoku_chikou', float('nan')),
                                            # إضافة حالة الاشتراك
                                            'is_subscribed': subscription_system.is_subscribed(user_id) if user_id and subscription_system else False
                                        }
                                        chart_data, content_type = self.create_chart(symbol, user_market_data['df'], chart_indicators)
                                        if chart_data:
                                            logger.info(f"تم إنشاء مخطط جديد للعملة {symbol} باستخدام API المستخدم في الذاكرة (حجم البيانات: {len(chart_data)} بايت)")
                                            user_market_data['chart_data'] = chart_data
                                            user_market_data['chart_content_type'] = content_type
                                        else:
                                            logger.error(f"فشل في إنشاء مخطط للعملة {symbol} باستخدام API المستخدم")
                                    except Exception as chart_error:
                                        logger.error(f"خطأ في إنشاء المخطط البياني: {str(chart_error)}")

                                return user_market_data
                except Exception as user_api_error:
                    logger.warning(f"فشل في استخدام مفاتيح API الخاصة بالمستخدم {user_id}: {str(user_api_error)}")
                    # سنستمر باستخدام API العام

            # استخدام BinanceAPIManager للحصول على البيانات بالإطار الزمني المحدد
            klines = await binance_manager.get_klines(symbol, interval=interval, user_id=user_id)
            if not klines:
                return None

            # تحويل البيانات إلى DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])

            # تحويل أعمدة الأسعار والحجم إلى float
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'quote_volume']
            df[numeric_columns] = df[numeric_columns].astype(float)

            # تحويل timestamp إلى datetime
            df['Date'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('Date', inplace=True)

            # حساب المؤشرات الفنية مع التحقق من الأخطاء
            try:
                close_prices = df['close']
                if len(close_prices) < 2:
                    logger.error("Insufficient price data")
                    return None

                current_price = close_prices.iloc[-1]

                # حساب التغير في السعر
                price_change = ((current_price - close_prices.iloc[-2]) / close_prices.iloc[-2]) * 100

                # حساب المؤشرات
                rsi = self.calculate_rsi(close_prices)
                ema20 = self.calculate_ema(close_prices, 20)
                ema50 = self.calculate_ema(close_prices, 50)
                macd, signal, histogram = self.calculate_macd(close_prices)
                bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(close_prices)
                stoch_k, stoch_d = self.calculate_stoch_rsi(close_prices)
                adx, plus_di, minus_di = self.calculate_adx(df['high'], df['low'], close_prices)
                # حساب مؤشر Ichimoku Cloud (للمستخدمين المشتركين فقط)
                tenkan_sen, kijun_sen, senkou_span_a, senkou_span_b, chikou_span = self.calculate_ichimoku_cloud(df['high'], df['low'], close_prices)

                # تجميع المؤشرات
                indicators = {
                    'rsi': rsi,
                    'ema20': ema20,
                    'ema50': ema50,
                    'macd': macd,
                    'macd_signal': signal,
                    'macd_histogram': histogram,
                    'bb_upper': bb_upper,
                    'bb_middle': bb_middle,
                    'bb_lower': bb_lower,
                    'stoch_k': stoch_k,
                    'stoch_d': stoch_d,
                    'adx': adx,
                    'plus_di': plus_di,
                    'minus_di': minus_di,
                    # إضافة مؤشر Ichimoku Cloud
                    'ichimoku_tenkan': tenkan_sen,
                    'ichimoku_kijun': kijun_sen,
                    'ichimoku_senkou_a': senkou_span_a,
                    'ichimoku_senkou_b': senkou_span_b,
                    'ichimoku_chikou': chikou_span
                }

                # التحقق من حالة اشتراك المستخدم
                is_subscribed = False
                if user_id and subscription_system:
                    is_subscribed = subscription_system.is_subscribed(user_id)

                # إضافة حالة الاشتراك إلى المؤشرات
                indicators['is_subscribed'] = is_subscribed

                # إنشاء المخطط البياني في الذاكرة
                chart_data, content_type = self.create_chart(symbol, df, indicators)

                # التحقق من البيانات
                if chart_data:
                    logger.info(f"تم إنشاء مخطط جديد للعملة {symbol} في الذاكرة (حجم البيانات: {len(chart_data)} بايت)")
                else:
                    logger.error(f"فشل في إنشاء مخطط للعملة {symbol} في الذاكرة")

            except Exception as e:
                logger.error(f"Error calculating indicators: {str(e)}")
                return None

            # تجميع البيانات
            market_data = {
                'symbol': symbol,  # إضافة رمز العملة للبيانات
                'price': current_price,
                'price_change': price_change,
                'currency': target_currency,  # تخزين العملة المستهدفة
                'rsi': rsi.iloc[-1] if not rsi.empty else float('nan'),
                'ema20': ema20.iloc[-1] if not ema20.empty else float('nan'),
                'ema50': ema50.iloc[-1] if not ema50.empty else float('nan'),
                'macd': macd,
                'macd_signal': signal,
                'macd_histogram': histogram,
                'bb_upper': bb_upper,
                'bb_middle': bb_middle,
                'bb_lower': bb_lower,
                'stoch_k': stoch_k,
                'stoch_d': stoch_d,
                'adx': adx,
                'plus_di': plus_di,
                'minus_di': minus_di,
                # إضافة مؤشر Ichimoku Cloud
                'ichimoku_tenkan': tenkan_sen,
                'ichimoku_kijun': kijun_sen,
                'ichimoku_senkou_a': senkou_span_a,
                'ichimoku_senkou_b': senkou_span_b,
                'ichimoku_chikou': chikou_span,
                # إضافة حالة الاشتراك
                'is_subscribed': is_subscribed,
                'chart_data': chart_data,
                'chart_content_type': content_type,
                'df': df  # إضافة DataFrame للاستخدام في إنشاء الرسم البياني
            }

            # إضافة التوصيات مع تحديد اللغة
            market_data['recommendations'] = self.get_recommendation(market_data, lang)

            # تخزين في الذاكرة المحلية
            self.market_data_cache[cache_key] = market_data
            self.market_data_expiry[cache_key] = current_time + timedelta(seconds=self.cache_timeout)

            return market_data

        except Exception as e:
            logger.error(f"Error getting market data: {str(e)}")
            return None

    def calculate_indicators(self, df):
        """حساب جميع المؤشرات الفنية"""
        try:
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            # Calculate other indicators
            ema20 = self.calculate_ema(df['close'], 20)
            ema50 = self.calculate_ema(df['close'], 50)
            macd, signal, hist = self.calculate_macd(df['close'])
            bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(df['close'])
            stoch_k, stoch_d = self.calculate_stoch_rsi(df['close'])
            adx, plus_di, minus_di = self.calculate_adx(df['high'], df['low'], df['close'])

            current_price = df['close'].iloc[-1]

            market_data = {
                'symbol': df.name if hasattr(df, 'name') else "UNKNOWN",  # إضافة رمز العملة للبيانات
                'price': current_price,
                'price_change': ((current_price - df['close'].iloc[-2]) / df['close'].iloc[-2]) * 100,
                'volume': df['volume'].iloc[-1],
                'rsi': rsi.iloc[-1],
                'ema20': ema20.iloc[-1],
                'ema50': ema50.iloc[-1],
                'macd': macd,
                'macd_signal': signal,
                'macd_hist': hist,
                'bb_upper': bb_upper,
                'bb_middle': bb_middle,
                'bb_lower': bb_lower,
                'stoch_k': stoch_k,
                'stoch_d': stoch_d,
                'adx': adx,
                'plus_di': plus_di,
                'minus_di': minus_di
            }

            # Get recommendations
            recommendations = self.get_recommendation(market_data)
            market_data['recommendations'] = recommendations

            return market_data

        except Exception as e:
            logger.error(f"Error calculating indicators: {str(e)}")
            return None

    def calculate_rsi(self, prices, period=14):
        """حساب مؤشر القوة النسبية (RSI)"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

# Initialize Crypto Analysis
ca = CryptoAnalysis()

async def show_language_selection(update: Update, context: CallbackContext):
    """عرض اختيار اللغة للمستخدم الجديد"""
    try:
        user_id = str(update.effective_user.id)

        # إنشاء أزرار اختيار اللغة
        keyboard = [
            [InlineKeyboardButton("🇸🇦 العربية", callback_data='set_initial_lang_ar')],
            [InlineKeyboardButton("🇬🇧 English", callback_data='set_initial_lang_en')]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        # إرسال رسالة اختيار اللغة
        await update.message.reply_text(
            "🌐 اختر لغتك المفضلة | Choose your preferred language",
            reply_markup=reply_markup
        )
    except Exception as e:
        logger.error(f"خطأ في عرض اختيار اللغة: {str(e)}")
        await update.message.reply_text("❌ حدث خطأ أثناء عرض اختيار اللغة. الرجاء المحاولة مرة أخرى لاحقًا.")

async def show_terms_and_conditions(update: Update, context: CallbackContext, lang='ar'):
    """عرض الشروط والأحكام للمستخدم"""
    try:
        user_id = str(update.effective_user.id)
        logger.info(f"جاري إعداد أزرار الشروط والأحكام للمستخدم {user_id} باللغة {lang}")

        # استخدام نصوص ثابتة بدلاً من قاموس الترجمة
        agree_button_text = "✅ أوافق على الشروط والأحكام" if lang == 'ar' else "✅ I Agree to Terms"
        decline_button_text = "❌ لا أوافق" if lang == 'ar' else "❌ I Disagree"

        # إنشاء أزرار الموافقة أو الرفض
        keyboard = [
            [InlineKeyboardButton(agree_button_text, callback_data='terms_agree')],
            [InlineKeyboardButton(decline_button_text, callback_data='terms_decline')]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        # استخدام نصوص ثابتة للشروط والأحكام
        terms_title = "📜 الشروط والأحكام" if lang == 'ar' else "📜 Terms and Conditions"

        # محتوى الشروط والأحكام باللغة العربية
        terms_content_ar = """⚠ إخلاء المسؤولية القانوني

1. هذا البوت يقدم تحليلات فنية فقط وليست نصائح استثمارية
2. أنت المسؤول الوحيد عن قراراتك الاستثمارية
3. لا نتحمل مسؤولية أي خسائر مالية
4. استخدام البوت يعني موافقتك على الشروط
5. عدم إعطاء مفاتيح الوصول بدقة قد يؤدي لفقدان اشتراكك
6. لا يوجد دعم فني - اتبع التعليمات بدقة
7. المؤشرات الفنية لا يمكن أن تكون 100% دقيقة وتعتمد على بيانات تاريخية
8. الأسواق المالية متقلبة تنطوي على مخاطر عالية
9. لا نضمن أي أرباح أو عوائد مالية
10. البوت قد يتعطل في أي وقت دون إشعار مسبق
11. نحن بريئون تماما من أي استخدام للبوت في تداول العملات
12. أي قرارات تداول تتخذها بناء على تحليلات البوت هي مسؤوليتك الكاملة
13. مطور البوت غير مسؤول عن أي خسائر مالية قد تنتج من استخدام البوت
14. استخدام البوت للتداول يتم على مسؤوليتك الشخصية الكاملة"""

        # محتوى الشروط والأحكام باللغة الإنجليزية
        terms_content_en = """⚠ Legal Disclaimer

1. This bot provides technical analysis only and not investment advice
2. You are solely responsible for your investment decisions
3. We do not assume responsibility for any financial losses
4. Using the bot means you agree to the terms
5. Failure to provide access keys accurately may result in loss of subscription
6. No technical support - follow instructions carefully
7. Technical indicators cannot be 100% accurate and rely on historical data
8. Financial markets are volatile and involve high risks
9. We do not guarantee any profits or financial returns
10. The bot may malfunction at any time without prior notice
11. We are completely innocent of any use of the bot in currency trading
12. Any trading decisions you make based on bot analysis are your complete responsibility
13. The bot developer is not responsible for any financial losses that may result from using the bot
14. Using the bot for trading is done at your own complete personal responsibility"""

        # اختيار محتوى الشروط والأحكام حسب اللغة المختارة
        terms_content = terms_content_ar if lang == 'ar' else terms_content_en

        # إضافة نص إضافي للشروط والأحكام
        terms_footer_ar = """
🔒 شروط الاستخدام:
• البوت للتحليل الفني فقط
• لا نضمن دقة التحليلات
• لا نقدم استشارات مالية
• الاشتراك غير قابل للاسترداد
• استخدام البوت لأغراض غير مشروعة ممنوع
• يحظر مشاركة حساب الاشتراك مع الآخرين
• نحتفظ بحق إيقاف أي حساب يخالف الشروط
• قد نقوم بتحديث الشروط في أي وقت دون إشعار
• البيانات المقدمة قد تكون متأخرة عن السوق الفعلي
• لا نتحمل مسؤولية أي أعطال فنية أو انقطاع في الخدمة

⚠ تحذيرات إضافية:
• تداول العملات الرقمية ينطوي على مخاطر عالية
• قد تخسر كل استثماراتك
• لا تستثمر أكثر مما يمكنك تحمل خسارته"""

        terms_footer_en = """
🔒 Terms of Use:
• The bot is for technical analysis only
• We do not guarantee the accuracy of analyses
• We do not provide financial advice
• Subscription is non-refundable
• Using the bot for illegal purposes is prohibited
• Sharing subscription accounts with others is prohibited
• We reserve the right to suspend any account that violates the terms
• We may update the terms at any time without notice
• The data provided may be delayed from the actual market
• We are not responsible for any technical failures or service interruptions

⚠ Additional Warnings:
• Cryptocurrency trading involves high risks
• You may lose all your investments
• Do not invest more than you can afford to lose"""

        # إضافة النص الإضافي حسب اللغة المختارة
        terms_footer = terms_footer_ar if lang == 'ar' else terms_footer_en

        # دمج محتوى الشروط والأحكام مع النص الإضافي
        terms_content = terms_content + "\n\n" + terms_footer

        logger.info(f"تم إعداد نصوص الشروط والأحكام للمستخدم {user_id}")

        # إعداد نص الرسالة
        message_text = f"{terms_title}\n\n{terms_content}"

        if update.callback_query:
            # إذا كان الطلب من خلال زر، نستخدم رسالة الاستعلام
            await update.callback_query.message.reply_text(
                message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.HTML
            )
        else:
            # إذا كان الطلب من خلال أمر، نستخدم رسالة المستخدم
            await update.message.reply_text(
                message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.HTML
            )
    except Exception as e:
        logger.error(f"خطأ في عرض الشروط والأحكام: {str(e)}")
        try:
            # محاولة إرسال رسالة خطأ باستخدام الكائن المناسب
            if update.callback_query and update.callback_query.message:
                await update.callback_query.message.reply_text("❌ حدث خطأ أثناء عرض الشروط والأحكام. الرجاء المحاولة مرة أخرى لاحقًا.")
            elif update.message:
                await update.message.reply_text("❌ حدث خطأ أثناء عرض الشروط والأحكام. الرجاء المحاولة مرة أخرى لاحقًا.")
            else:
                logger.error("لا يمكن إرسال رسالة خطأ: لا يوجد كائن رسالة متاح")
        except Exception as inner_e:
            logger.error(f"خطأ في إرسال رسالة الخطأ: {str(inner_e)}")

async def show_main_menu(update: Update, context: CallbackContext, new_message=False):
    """عرض القائمة الرئيسية"""
    try:
        user_id = str(update.effective_user.id)
        logger.info(f"جاري عرض القائمة الرئيسية للمستخدم {user_id}")

        # الحصول على إعدادات المستخدم
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')
        logger.info(f"لغة المستخدم {user_id}: {lang}")

        # التأكد من تهيئة مدير API
        global api_manager, encryption_key
        if api_manager is None:
            logger.info(f"جاري تهيئة مدير API لأول مرة في show_main_menu للمستخدم {user_id}...")
            success = await initialize_system()
            if not success:
                # إذا فشلت التهيئة، نقوم بإنشاء مدير API يدويًا
                logger.warning("فشلت تهيئة النظام، جاري إنشاء مدير API يدويًا...")
                if encryption_key is None:
                    encryption_key = Fernet.generate_key().decode()
                api_manager = APIManager(db, encryption_key)

        # التأكد من وجود مدير API قبل عرض القائمة
        if api_manager is None:
            error_msg = "❌ حدث خطأ في تهيئة النظام. الرجاء المحاولة مرة أخرى لاحقًا."
            logger.error("لم يتم تهيئة مدير API بشكل صحيح")
            if new_message or not update.callback_query:
                await update.effective_message.reply_text(error_msg)
            else:
                await update.callback_query.message.reply_text(error_msg)
            return

        # إنشاء نص القائمة ولوحة المفاتيح
        logger.info(f"جاري إنشاء نص القائمة للمستخدم {user_id}")
        menu_text = await get_main_menu_text(user_id, lang)
        keyboard = get_main_menu_keyboard(user_id, lang)

        # عرض القائمة
        if new_message or not update.callback_query:
            logger.info(f"إرسال رسالة جديدة للمستخدم {user_id}")
            await update.effective_message.reply_text(
                text=menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
        else:
            try:
                logger.info(f"تحديث الرسالة الحالية للمستخدم {user_id}")
                await update.callback_query.edit_message_text(
                    text=menu_text,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.HTML
                )
            except Exception as e:
                # إذا فشل تعديل الرسالة، نرسل رسالة جديدة
                logger.warning(f"فشل تحديث الرسالة، إرسال رسالة جديدة للمستخدم {user_id}: {str(e)}")
                await update.callback_query.message.reply_text(
                    text=menu_text,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.HTML
                )

        logger.info(f"تم عرض القائمة الرئيسية للمستخدم {user_id} بنجاح")

    except Exception as e:
        logger.error(f"خطأ في عرض القائمة الرئيسية: {str(e)}")
        try:
            # محاولة إرسال رسالة خطأ بسيطة
            error_text = "❌ حدث خطأ في عرض القائمة. الرجاء المحاولة مرة أخرى لاحقًا."
            if update.callback_query:
                await update.callback_query.answer(
                    get_text('error_occurred', 'ar')
                )
                await update.callback_query.message.reply_text(error_text)
            else:
                await update.effective_message.reply_text(error_text)
        except Exception as inner_e:
            logger.error(f"خطأ في إرسال رسالة الخطأ: {str(inner_e)}")

async def start(update: Update, context: CallbackContext):
    """بداية استخدام البوت"""
    try:
        user_id = str(update.effective_user.id)
        username = update.effective_user.username or "غير معروف"

        logger.info(f"معالجة أمر /start للمستخدم {user_id}")

        # تم تعطيل تشغيل رابط Koyeb لأنه لم يعد ضروريًا

        # التحقق من حظر المستخدم
        banned_ref = db.collection('banned_users').document(user_id).get()
        if banned_ref.exists and banned_ref.to_dict().get('status') == 'banned':
            logger.info(f"محاولة وصول من مستخدم محظور: {user_id}")
            await update.message.reply_text("⛔️ عذراً، تم حظر حسابك من استخدام البوت. يرجى التواصل مع المطور إذا كنت تعتقد أن هذا خطأ.")
            return

        # إضافة المستخدم إلى قاعدة البيانات
        await add_user_to_users_collection(user_id, username)

        # التأكد من تهيئة مدير API
        global api_manager, encryption_key
        if api_manager is None:
            logger.info(f"جاري تهيئة مدير API لأول مرة للمستخدم {user_id}...")
            success = await initialize_system()
            if not success:
                # إذا فشلت التهيئة، نقوم بإنشاء مدير API يدويًا
                logger.warning("فشلت تهيئة النظام، جاري إنشاء مدير API يدويًا...")
                if encryption_key is None:
                    encryption_key = Fernet.generate_key().decode()
                api_manager = APIManager(db, encryption_key)

        # التأكد من وجود مدير API قبل عرض القائمة
        if api_manager is None:
            logger.error("لم يتم تهيئة مدير API بشكل صحيح")
            await update.message.reply_text("❌ حدث خطأ في تهيئة النظام. الرجاء المحاولة مرة أخرى لاحقًا.")
            return

        # التحقق من إعدادات المستخدم
        user_settings = subscription_system.get_user_settings(user_id)
        lang_selected = user_settings.get('lang_selected', False)  # هل اختار المستخدم اللغة من قبل
        terms_accepted = user_settings.get('terms_accepted', False)  # هل وافق المستخدم على الشروط والأحكام

        # إذا لم يختر المستخدم اللغة بعد، عرض اختيار اللغة
        if not lang_selected:
            logger.info(f"عرض اختيار اللغة للمستخدم {user_id}")
            await show_language_selection(update, context)
            return

        # إذا لم يوافق المستخدم على الشروط والأحكام بعد، عرضها
        if not terms_accepted:
            lang = user_settings.get('lang', 'ar')
            logger.info(f"عرض الشروط والأحكام للمستخدم {user_id}")
            await show_terms_and_conditions(update, context, lang)
            return

        # عرض القائمة الرئيسية
        logger.info(f"عرض القائمة الرئيسية للمستخدم {user_id}")
        await show_main_menu(update, context, new_message=True)
        logger.info(f"تم عرض القائمة الرئيسية للمستخدم {user_id} بنجاح")

    except Exception as e:
        logger.error(f"خطأ في معالجة أمر /start: {str(e)}")
        # محاولة إرسال رسالة بسيطة على الأقل
        try:
            await update.message.reply_text("❌ حدث خطأ أثناء بدء البوت. الرجاء المحاولة مرة أخرى لاحقًا.")
        except Exception as inner_e:
            logger.error(f"خطأ في إرسال رسالة الخطأ: {str(inner_e)}")

async def button_click(update: Update, context: CallbackContext):
    """معالجة النقر على الأزرار"""
    try:
        query = update.callback_query
        user_id = str(query.from_user.id)

        # التحقق من حظر المستخدم
        banned_ref = db.collection('banned_users').document(user_id).get()
        if banned_ref.exists and banned_ref.to_dict().get('status') == 'banned':
            logger.info(f"محاولة وصول من مستخدم محظور: {user_id}")
            await query.answer("⛔️ عذراً، تم حظر حسابك من استخدام البوت", show_alert=True)
            return

        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # معالجة اختيار اللغة الأولي
        if query.data.startswith('set_initial_lang_'):
            selected_lang = query.data.split('_')[-1]  # ar أو en
            logger.info(f"المستخدم {user_id} اختار اللغة: {selected_lang}")

            # تحديث إعدادات المستخدم مع اللغة المختارة
            success = subscription_system.update_user_settings(user_id, lang=selected_lang, lang_selected=True)
            if success:
                logger.info(f"تم تحديث لغة المستخدم {user_id} إلى {selected_lang}")

                # إرسال رسالة تأكيد اختيار اللغة
                confirm_message = "تم اختيار اللغة العربية بنجاح" if selected_lang == 'ar' else "English language selected successfully"
                await query.answer(confirm_message)

                # عرض الشروط والأحكام بعد اختيار اللغة
                logger.info(f"جاري عرض الشروط والأحكام للمستخدم {user_id} باللغة {selected_lang}")
                await show_terms_and_conditions(update, context, selected_lang)
            else:
                error_message = "حدث خطأ أثناء حفظ الإعدادات. الرجاء المحاولة مرة أخرى" if selected_lang == 'ar' else "Error saving settings. Please try again"
                await query.answer(error_message, show_alert=True)
            return

        # معالجة الموافقة على الشروط والأحكام
        elif query.data == 'terms_agree':
            logger.info(f"المستخدم {user_id} يحاول الموافقة على الشروط والأحكام")

            # تحديث إعدادات المستخدم لتسجيل الموافقة على الشروط
            success = subscription_system.update_user_settings(user_id, terms_accepted=True)
            if success:
                logger.info(f"المستخدم {user_id} وافق على الشروط والأحكام")

                # استخدام نصوص ثابتة بدلاً من قاموس الترجمة
                agree_message = "شكراً لموافقتك على الشروط والأحكام" if lang == 'ar' else "Thank you for agreeing to the terms and conditions"

                # إرسال رسالة تأكيد
                await query.answer(agree_message)

                # عرض القائمة الرئيسية بعد الموافقة على الشروط
                await show_main_menu(update, context, new_message=True)
            else:
                # رسالة خطأ في حالة فشل تحديث الإعدادات
                error_message = "حدث خطأ أثناء حفظ الإعدادات. الرجاء المحاولة مرة أخرى" if lang == 'ar' else "Error saving settings. Please try again"
                await query.answer(error_message, show_alert=True)
            return

        # معالجة رفض الشروط والأحكام
        elif query.data == 'terms_decline':
            logger.info(f"المستخدم {user_id} رفض الشروط والأحكام")

            # استخدام نصوص ثابتة بدلاً من قاموس الترجمة
            decline_message = "للأسف، يجب الموافقة على الشروط والأحكام لاستخدام البوت" if lang == 'ar' else "Sorry, you must agree to the terms and conditions to use the bot"
            required_message = "للأسف، يجب الموافقة على الشروط والأحكام لاستخدام البوت. يمكنك إعادة تشغيل البوت في أي وقت للموافقة على الشروط. 🔄" if lang == 'ar' else "Sorry, you must agree to the terms and conditions to use the bot. You can restart the bot at any time to agree to the terms. 🔄"

            # إرسال رسالة تنبيه ورسالة توضيحية
            await query.answer(decline_message, show_alert=True)
            await query.message.reply_text(required_message)
            return

        # معالجة إضافة العملة
        elif query.data.startswith('add_currency_'):
            currency_code = query.data.split('_')[2]

            # تحميل الإعدادات الحالية
            custom_currencies = settings.get('currencies', [])

            # إضافة العملة إذا لم تكن موجودة
            if currency_code not in custom_currencies:
                custom_currencies.append(currency_code)

                # حفظ التغييرات
                settings['currencies'] = custom_currencies
                if subscription_system.update_user_settings(user_id, currencies=custom_currencies):
                    await query.answer(
                        get_text('currency_added', lang).format(currency=currency_code),
                        show_alert=True
                    )
                else:
                    await query.answer(
                        get_text('error_saving_currency', lang),
                        show_alert=True
                    )
            else:
                await query.answer(
                    get_text('currency_exists', lang),
                    show_alert=True
                )

            # العودة إلى قائمة إدارة العملات
            await subscription_system.manage_currencies(update, context)
            return

        # معالجة حذف العملة
        elif query.data.startswith('remove_currency_'):
            currency_code = query.data.split('_')[2]

            # تحميل الإعدادات الحالية
            custom_currencies = settings.get('currencies', [])

            # حذف العملة إذا كانت موجودة
            if currency_code in custom_currencies:
                custom_currencies.remove(currency_code)

                # حفظ التغييرات
                settings['currencies'] = custom_currencies
                if subscription_system.update_user_settings(user_id, currencies=custom_currencies):
                    await query.answer(
                        get_text('currency_removed', lang).format(currency=currency_code),
                        show_alert=True
                    )
                else:
                    await query.answer(
                        get_text('error_saving_currency', lang),
                        show_alert=True
                    )

            # العودة إلى قائمة إدارة العملات
            await subscription_system.manage_currencies(update, context)
            return

        if query.data.startswith('toggle_indicator_'):
            parts = query.data.split('_')
            if len(parts) >= 4:
                symbol = parts[2]
                indicator_id = parts[3]

                # تحميل الإعدادات الحالية
                settings = load_user_settings(user_id)
                indicators = settings.get('indicators', [])

                # التحقق مما إذا كان المؤشر موجوداً
                is_active = any(ind.get('id') == indicator_id for ind in indicators)

                if is_active:
                    # إزالة المؤشر
                    indicators = [ind for ind in indicators if ind.get('id') != indicator_id]
                    action = get_text('removed', lang)
                else:
                    # إضافة المؤشر
                    indicators.append({'id': indicator_id})
                    action = get_text('added', lang)

                # حفظ التغييرات
                if save_user_settings(user_id, indicators=indicators):
                    # إرسال إشعار popup سريع
                    indicator_name = get_text(f'{indicator_id}_indicator', lang)
                    notification_text = "تم تحديث المؤشرات" if lang == 'ar' else "Indicators updated"
                    await query.answer(text=notification_text, show_alert=False, cache_time=1)

                    # تحديث التحليل
                    await analyze_symbol(update, context, symbol)
                else:
                    await query.answer(text=get_text('error_saving_indicator', lang), show_alert=False, cache_time=1)
                return

        elif query.data == 'analyze':
            warning_text = get_text('enter_symbol', lang)

            # التحقق من الاستخدام المجاني
            if not subscription_system.is_subscribed(user_id):
                free_usage = subscription_system.get_free_usage(user_id)
                if free_usage['analyses'] <= 1:
                    warning_text = (
                        "⚠️ لا توجد تنبيهات نشطة حالياً (تحذير: سيتم استخدام تحليل عملة)\n\n" + warning_text
                        if lang == 'ar' else
                        "⚠️ No active alerts (Warning: this will use a currency analysis)\n\n" + warning_text
                    )

            await query.message.reply_text(
                warning_text,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')
                ]])
            )
            user_states[user_id] = {'state': 'waiting_for_symbol'}
            await query.answer()
            return

        elif query.data == 'active_alerts':
            # جلب التنبيهات النشطة من Firestore
            alerts_ref = db.collection('alerts').document(user_id)
            alerts_data = alerts_ref.get()

            if not alerts_data.exists or not alerts_data.to_dict():
                # لا توجد تنبيهات نشطة
                keyboard = [
                    [InlineKeyboardButton(get_text('add_new_alert', lang), callback_data='analyze')],
                    [InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]
                ]
                no_alerts_text = (
                    "⚠️ لا توجد تنبيهات نشطة حالياً (سيتم استخدام تحليل واحد اذا قمت بالنقر على تنبيه جديد)" if lang == 'ar' else
                    "⚠️ No active alerts (This will use one analysis)"
                )
                await query.edit_message_text(
                    text=no_alerts_text,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
                return

            # تجميع التنبيهات النشطة
            alerts = alerts_data.to_dict()
            alerts_text = f"🔔 {get_text('active_alerts_title', lang)}\n\n"

            # عرض عدد التنبيهات النشطة
            total_alerts = sum(len(symbol_alerts) for symbol_alerts in alerts.values())
            alerts_text += f"📊 {get_text('total_alerts', lang)}: {total_alerts}\n\n"

            # تنظيم التنبيهات حسب العملة
            for symbol, symbol_alerts in alerts.items():
                alerts_text += f"💱 *{symbol}*\n"
                for i, alert in enumerate(symbol_alerts, 1):
                    condition = get_text('alert_condition_above', lang) if alert['condition'] == 'above' else get_text('alert_condition_below', lang)
                    alerts_text += f"{i}. {condition} {alert['price']:.4f}\n"
                alerts_text += "\n"

            # إضافة أزرار التحكم
            keyboard = [
                [InlineKeyboardButton(get_text('add_new_alert', lang), callback_data='analyze')],
                [InlineKeyboardButton(get_text('clear_alerts', lang), callback_data='clear_alerts')],
                [InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]
            ]

            await query.edit_message_text(
                text=alerts_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
            return

        elif query.data == 'clear_alerts':
            # حذف جميع التنبيهات
            alerts_ref = db.collection('alerts').document(user_id)
            alerts_ref.delete()

            # إعادة تعيين عدد التنبيهات المجانية
            if not subscription_system.is_subscribed(user_id):
                usage_ref = db.collection('free_usage').document(user_id)
                current_usage = usage_ref.get().to_dict()
                if current_usage:
                    current_usage['alerts'] = 1
                    current_usage['date'] = datetime.now().date().isoformat()
                    usage_ref.set(current_usage)

                    # تحديث الذاكرة المحلية
                    current_time = datetime.now()
                    subscription_system._free_usage_cache[user_id] = current_usage
                    subscription_system._free_usage_expiry[user_id] = current_time + timedelta(hours=1)

            await query.answer(get_text('alerts_cleared', lang))
            await show_main_menu(update, context)
            return

        if query.data == 'upgrade':
            # عرض معلومات الترقية
            await show_upgrade_info(update, context)
            return

        elif query.data == 'payment_paypal':
            # معالجة الدفع عبر PayPal
            from services.handle_paypal_payment import handle_paypal_payment
            try:
                await handle_paypal_payment(update, context)
            except Exception as e:
                logger.error(f"خطأ في معالجة الدفع عبر PayPal: {str(e)}")
                await query.answer("❌ حدث خطأ أثناء معالجة الدفع. الرجاء المحاولة مرة أخرى.", show_alert=True)
                # إعادة توجيه المستخدم إلى القائمة الرئيسية
                await show_main_menu(update, context, new_message=True)
            return

        elif query.data == 'verify_payment' or query.data.startswith('verify_payment_'):
            # التحقق من الدفع
            await handle_payment_verification(update, context)
            return

        elif query.data == 'language':
            # إنشاء أزرار اختيار اللغة
            keyboard = [
                [
                    InlineKeyboardButton("🇸🇦 العربية", callback_data='set_lang_ar'),
                    InlineKeyboardButton("🇬🇧 English", callback_data='set_lang_en')
                ],
                [InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]
            ]
            await query.edit_message_text(
                text=get_text('choose_language', lang),
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        elif query.data == 'free_day_settings':
            # إدارة إعدادات اليوم المجاني
            await manage_free_day_settings(update, context)
            return

        elif query.data.startswith('set_free_day_'):
            # تعيين اليوم المجاني
            await set_free_day(update, context)
            return

        elif query.data.startswith('set_lang_'):
            # تغيير اللغة
            new_lang = query.data.split('_')[2]
            # تحديث اللغة في الإعدادات
            settings['lang'] = new_lang
            subscription_system.update_user_settings(user_id, **settings)

            # تحديث الذاكرة المؤقتة
            firestore_cache.set(f'settings_{user_id}', settings, ex=subscription_system.cache_timeout, cache_type="user_data")

            # إرسال رسالة تأكيد باللغة الجديدة
            success_message = "✅ Language changed to English!" if new_lang == 'en' else "✅ تم تغيير اللغة إلى العربية!"
            await query.answer(success_message)

            # تحديث القائمة الرئيسية باللغة الجديدة
            await show_main_menu(update, context)
            return

        elif query.data == 'back_to_main':
            await show_main_menu(update, context)
            return

        elif query.data == 'setup_api_keys':
            await setup_api_keys(update, context, api_manager, subscription_system)
            return

        elif query.data == 'select_platform':
            await show_platform_selection(update, context, api_manager, subscription_system)
            return

        elif query.data == 'setup_binance_api':
            await show_api_instructions(update, context, 'binance', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'binance_key'}
            return

        elif query.data == 'setup_gemini_api':
            # التحقق من حالة الاشتراك
            if not subscription_system.is_subscribed(user_id):
                await query.answer(
                    "هذه الميزة متاحة للمشتركين فقط. يرجى الترقية للوصول إليها." if lang == 'ar' else
                    "This feature is available for subscribers only. Please upgrade to access it.",
                    show_alert=True
                )
                return

            await show_api_instructions(update, context, 'gemini', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'gemini_key'}
            return

        elif query.data == 'setup_kucoin_api':
            await show_api_instructions(update, context, 'kucoin', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'kucoin_key'}
            return

        elif query.data == 'setup_coinbase_api':
            await show_api_instructions(update, context, 'coinbase', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'coinbase_key'}
            return

        elif query.data == 'setup_bybit_api':
            await show_api_instructions(update, context, 'bybit', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'bybit_key'}
            return

        elif query.data == 'setup_okx_api':
            await show_api_instructions(update, context, 'okx', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'okx_key'}
            return

        elif query.data == 'setup_kraken_api':
            await show_api_instructions(update, context, 'kraken', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'kraken_key'}
            return



        elif query.data == 'delete_api_keys':
            await delete_api_keys_ui(update, context, api_manager, subscription_system)
            return

        elif query.data.startswith('delete_') and query.data.endswith('_api'):
            # حذف مفاتيح API للمنصات
            platform = query.data.replace('delete_', '').replace('_api', '')
            if platform in ['binance', 'gemini', 'kucoin', 'coinbase', 'bybit', 'okx', 'kraken']:
                if await api_manager.delete_api_keys(user_id, platform):
                    await query.answer(
                        f"تم حذف مفتاح {platform.capitalize()} API بنجاح." if lang == 'ar' else
                        f"{platform.capitalize()} API key deleted successfully.",
                        show_alert=True
                    )
                else:
                    await query.answer(
                        "حدث خطأ أثناء حذف مفتاح API." if lang == 'ar' else
                        "An error occurred while deleting API key.",
                        show_alert=True
                    )

                # إعادة توجيه المستخدم إلى صفحة إعداد API
                await setup_api_keys(update, context, api_manager, subscription_system)
                return



        elif query.data == 'analyze':
            # إرسال رسالة طلب رمز العملة
            await query.message.reply_text(
                get_text('enter_symbol', lang),
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')
                ]])
            )
            # تحديث حالة المستخدم
            user_states[user_id] = 'waiting_for_symbol'
            await query.answer()
            return

        # معالجة أزرار التنبيه السعري
        elif query.data.startswith('alert_'):
            parts = query.data.split('_')
            if len(parts) >= 4:
                condition = parts[1]  # above or below
                symbol = parts[2]
                price = float(parts[3])

                # التحقق من حدود التنبيهات المجانية
                if not subscription_system.is_subscribed(user_id):
                    if not subscription_system.use_free_alert(user_id):
                        await query.answer(
                            "عذراً، لقد استنفدت عدد التنبيهات المجانية" if lang == 'ar' else
                            "Sorry, you've used all your free alerts",
                            show_alert=True
                        )
                        return

                # إضافة التنبيه
                alerts_ref = db.collection('alerts').document(user_id)
                alert_data = alerts_ref.get()

                if alert_data.exists:
                    user_alerts = alert_data.to_dict()
                else:
                    user_alerts = {}

                if symbol not in user_alerts:
                    user_alerts[symbol] = []

                # إضافة التنبيه الجديد
                user_alerts[symbol].append({
                    'price': price,
                    'condition': condition,
                    'created_at': datetime.now().isoformat()
                })

                # حفظ التنبيهات
                alerts_ref.set(user_alerts)

                # إرسال رسالة تأكيد
                condition_text = get_text('alert_condition_above', lang) if condition == 'above' else get_text('alert_condition_below', lang)
                await query.answer(
                    get_text('alert_added', lang).format(
                        symbol=symbol,
                        condition=condition_text,
                        price=price
                    ),
                    show_alert=True
                )

                # العودة إلى القائمة الرئيسية
                await show_main_menu(update, context)
                return

        elif query.data.startswith('quick_alert_'):
            symbol = query.data.split('_')[2]
            await setup_price_alert(update, context, symbol)
            return


        # معالجة أزرار الميزات المتقدمة
        elif query.data.startswith('trading_strategy_'):
            symbol = query.data.split('_')[2]
            await get_trading_strategy_analysis(update, context, symbol)
            return

        elif query.data.startswith('price_prediction_'):
            symbol = query.data.split('_')[2]
            await get_price_prediction_analysis(update, context, symbol)
            return

        elif query.data.startswith('multi_timeframe_'):
            symbol = query.data.split('_')[2]
            await get_multi_timeframe_analysis_view(update, context, symbol)
            return

        elif query.data.startswith('ichimoku_cloud_'):
            symbol = query.data.split('_')[2]
            await get_ichimoku_cloud_analysis(update, context, symbol)
            return

        # تم إزالة معالجة أزرار التقارير الدورية

        # تم إزالة معالج التقارير الدورية

        elif query.data == 'manage_currencies':
            await subscription_system.manage_currencies(update, context)
        elif query.data == 'add_currency':
            await subscription_system.add_currency(update, context)
        elif query.data == 'remove_currency':
            await subscription_system.remove_currency(update, context)

        # تم إزالة معالج حذف التقارير الدورية

        # تم إزالة معالج حذف تقرير دوري محدد

        # تم إزالة معالج التقارير الدورية

        # تم إزالة معالج إعداد فترة مخصصة للتقارير الدورية

        elif query.data == 'help':
            help_text = get_text('help_text', lang)
            keyboard = [[InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]]
            await query.edit_message_text(
                text=help_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
            return

        elif query.data.startswith('custom_alert_'):
            symbol = query.data.split('_')[2]
            await handle_custom_alert(update, context, symbol)
            return

        elif query.data == 'ai_chat':
            # التحقق من حالة الاشتراك ووجود مفتاح Gemini API
            if not subscription_system.is_subscribed(user_id):
                await query.answer(
                    "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                    "This feature is available for subscribers only",
                    show_alert=True
                )
                return

            has_gemini_api = await api_manager.has_api_keys(user_id, 'gemini')
            if not has_gemini_api:
                await query.answer(
                    "يرجى إضافة مفتاح Gemini API الخاص بك أولاً" if lang == 'ar' else
                    "Please add your Gemini API key first",
                    show_alert=True
                )
                # توجيه المستخدم إلى صفحة إعدادات API
                await setup_api_keys(update, context, api_manager, subscription_system)
                return



            # بدء محادثة مع الذكاء الاصطناعي
            chat_text = (
                "🤖 *الدردشة مع الذكاء الاصطناعي*\n\n"
                "يمكنك الآن الدردشة مع الذكاء الاصطناعي والاستفسار عن أي شيء يتعلق بالعملات الرقمية والتداول.\n\n"
                "• اسأل عن عملة معينة للحصول على تحليل\n"
                "• استفسر عن استراتيجيات التداول\n"
                "• اطلب نصائح حول إدارة المخاطر\n\n"
                "أرسل رسالتك الآن..."
            ) if lang == 'ar' else (
                "🤖 *Chat with AI*\n\n"
                "You can now chat with AI and ask about anything related to cryptocurrencies and trading.\n\n"
                "• Ask about a specific cryptocurrency for analysis\n"
                "• Inquire about trading strategies\n"
                "• Request tips on risk management\n\n"
                "Send your message now..."
            )

            keyboard = [[InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]]
            await query.edit_message_text(
                text=chat_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )

            # تحديث حالة المستخدم
            user_states[user_id] = {'state': 'ai_chat'}
            return

        elif query.data == 'terms':
            # عرض الشروط والأحكام
            keyboard = [[InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]]

            # تحديد النص حسب اللغة المختارة
            if lang == 'en':
                terms_text = """
⚠️ *Legal Disclaimer*

1. This bot provides technical analysis only, not investment advice
2. You are solely responsible for your investment decisions
3. We are not liable for any financial losses
4. Using the bot implies acceptance of terms
5. Not following subscription steps may result in loss of access
6. No technical support - follow instructions carefully
7. Technical indicators may not be 100% accurate and rely on historical data
8. Financial markets are volatile and involve high risks
9. We do not guarantee any profits or returns
10. Bot may stop or malfunction at any time without notice
11. *We are completely not responsible for any use of the bot in trading currencies*
12. *Any trading decisions you make based on bot analysis are your full responsibility*

🔒 *Terms of Use*:
• Bot is for technical analysis only
• We do not guarantee analysis accuracy
• We do not provide financial advice
• Subscription is non-refundable
• Prohibited to use bot for illegal purposes
• Sharing subscription account is prohibited
• We reserve right to terminate violating accounts
• Terms may be updated without notice
• Data may be delayed from actual market
• Not responsible for technical issues or service interruptions

⚠️ *Additional Warnings*:
• Cryptocurrency trading involves high risks
• Do not invest more than you can afford to lose
• Conduct your own research before decisions
• Be cautious of scams and fraudulent projects
• Understand technical indicators before using them
• The bot developer is not responsible for any financial losses resulting from using the bot
• Using the bot for trading is done at your own full responsibility
"""
            else:
                terms_text = """
⚠️ *إخلاء المسؤولية القانوني*

1. هذا البوت يقدم تحليلات فنية فقط وليست نصائح استثمارية
2. أنت المسؤول الوحيد عن قراراتك الاستثمارية
3. لا نتحمل مسؤولية أي خسائر مالية
4. استخدام البوت يعني موافقتك على الشروط
5. عدم اتباع خطوات الاشتراك بدقة قد يؤدي لفقدان اشتراكك
6. لا يوجد دعم فني - اتبع التعليمات بدقة
7. المؤشرات الفنية قد لا تكون دقيقة 100% وتعتمد على بيانات تاريخية
8. الأسواق المالية متقلبة وتنطوي على مخاطر عالية
9. لا نضمن أي أرباح أو عوائد مالية
10. البوت قد يتوقف أو يتعطل في أي وقت دون إشعار مسبق
11. *نحن بريئون تماماً من أي استخدام للبوت في تداول العملات*
12. *أي قرارات تداول تتخذها بناءً على تحليلات البوت هي مسؤوليتك الكاملة*
13. *مطور البوت غير مسؤول عن أي خسائر مالية قد تنتج عن استخدام البوت*
14. *استخدام البوت للتداول يتم على مسؤوليتك الشخصية الكاملة*

🔒 *شروط الاستخدام*:
• البوت للتحليل الفني فقط
• لا نضمن دقة التحليلات
• لا نقدم استشارات مالية
• الاشتراك غير قابل للاسترداد
• يحظر استخدام البوت لأغراض غير مشروعة
• يحظر مشاركة حساب الاشتراك مع الآخرين
• نحتفظ بحق إيقاف أي حساب يخالف الشروط
• قد نقوم بتحديث الشروط في أي وقت دون إشعار
• البيانات المقدمة قد تكون متأخرة عن السوق الفعلي
• لا نتحمل مسؤولية أي أعطال فنية أو انقطاع في الخدمة

⚠️ *تحذيرات إضافية*:
• تداول العملات الرقمية ينطوي على مخاطر عالية
• لا تستثمر أكثر مما يمكنك تحمل خسارته
• قم بإجراء بحثك الخاص قبل اتخاذ أي قرار
• كن حذراً من عمليات الاحتيال والمشاريع الوهمية
• تأكد من فهم آلية عمل المؤشرات الفنية قبل استخدامها
"""

            await query.edit_message_text(
                text=terms_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
            return

        await query.answer()

    except Exception as e:
        logger.error(f"خطأ في معالجة الأزرار: {str(e)}")
        try:
            error_text = get_text('error_occurred', lang)
            await update.callback_query.answer(error_text, show_alert=True)
        except:
            pass



async def analyze_symbol(update: Update, context: CallbackContext, symbol: str, message=None, target_currency=None):
    """تحليل رمز العملة"""
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # استخدام العملة المخصصة إذا كانت متوفرة
        if target_currency is None:
            # استخدام أول عملة من قائمة العملات المخصصة للمستخدم
            custom_currencies = settings.get('currencies', [])
            if custom_currencies:
                target_currency = custom_currencies[0]
            else:
                target_currency = 'USD'  # العملة الافتراضية

        # تنظيف وتحقق من رمز العملة
        async def clean_symbol_async(symbol: str) -> str:
            # إزالة الفراغات والرموز الخاصة
            symbol = re.sub(r'[^A-Za-z0-9]', '', symbol.upper().strip())

            # التحقق من وجود حروف في الرمز
            if not any(c.isalpha() for c in symbol):
                raise ValueError("رمز عملة غير صالح")

            # إضافة USDT إذا لم يكن موجوداً (للعملات الرقمية)
            if not symbol.endswith('USDT'):
                symbol = f"{symbol}USDT"

            return symbol

        # استدعاء الدالة المتزامنة
        cleaned_symbol = await clean_symbol_async(symbol)

        # التحقق من صحة الرمز
        if len(cleaned_symbol) < 5:
            error_text = "Please enter a valid trading pair (e.g., BTC, ETH)" if lang == 'en' else "الرجاء إدخال رمز عملة صحيح (مثال: BTC, ETH)"
            if message:
                await message.reply_text(error_text)
            else:
                await update.message.reply_text(error_text)
            return

        # التحقق من حالة الاشتراك
        if not subscription_system.is_subscribed(user_id):
            free_usage = subscription_system.get_free_usage(user_id)
            if free_usage['analyses'] <= 0:
                limit_text = (
                    "You've used all your free analyses. Upgrade to get unlimited analyses!" if lang == 'en' else
                    "لقد استنفدت عدد التحليلات المجانية. قم بالترقية للحصول على تحليلات غير محدودة!"
                )
                keyboard = [[InlineKeyboardButton("✨ Upgrade" if lang == 'en' else "✨ ترقية", callback_data="upgrade")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                if message:
                    await message.reply_text(limit_text, reply_markup=reply_markup)
                else:
                    await update.message.reply_text(limit_text, reply_markup=reply_markup)
                return

        # إرسال رسالة انتظار
        wait_message = await (message.reply_text if message else update.message.reply_text)(
            "Analyzing market..." if lang == 'en' else "جاري تحليل السوق..."
        )

        try:
            # تحليل العملة باستخدام مفاتيح API الخاصة بالمستخدم إذا كانت متاحة
            market_data = await ca.get_market_data(cleaned_symbol, target_currency=target_currency, user_id=user_id, lang=lang)

            if not market_data:
                error_text = (
                    f"Could not find data for {cleaned_symbol}. Please check the symbol and try again." if lang == 'en' else
                    f"لم يتم العثور على بيانات لـ {cleaned_symbol}. الرجاء التحقق من الرمز والمحاولة مرة أخرى."
                )
                await wait_message.edit_text(error_text)
                return

            # تحديث الاستخدام المجاني
            if not subscription_system.is_subscribed(user_id):
                subscription_system.use_free_analysis(user_id)

            # إنشاء نص التحليل - تمرير معرف المستخدم
            analysis_text = await create_analysis_text(cleaned_symbol, market_data, lang, user_id)

            # إضافة أزرار التحكم مع الميزات المتقدمة
            keyboard = [
                [
                    InlineKeyboardButton("🔄 تحديث" if lang == 'ar' else "🔄 Refresh", callback_data=f'refresh_{cleaned_symbol}'),
                    InlineKeyboardButton("📊 تحليل عملة أخرى" if lang == 'ar' else "📊 Analyze Another", callback_data='analyze')
                ],
                [
                    InlineKeyboardButton("⏰ إعداد تنبيه" if lang == 'ar' else "⏰ Set Alert", callback_data=f'quick_alert_{cleaned_symbol}')
                ]
            ]

            # إضافة أزرار الميزات المتقدمة للمشتركين فقط الذين لديهم مفتاح Gemini API
            if subscription_system.is_subscribed(user_id) and await api_manager.has_api_keys(user_id, 'gemini'):
                advanced_buttons = [
                    [
                        InlineKeyboardButton("📈 استراتيجية تداول" if lang == 'ar' else "📈 Trading Strategy",
                                           callback_data=f'trading_strategy_{cleaned_symbol}'),
                        InlineKeyboardButton("🔮 تنبؤات سعرية" if lang == 'ar' else "🔮 Price Predictions",
                                           callback_data=f'price_prediction_{cleaned_symbol}')
                    ],
                    [
                        InlineKeyboardButton("🔍 تحليل متعدد الإطارات" if lang == 'ar' else "🔍 Multi-Timeframe Analysis",
                                           callback_data=f'multi_timeframe_{cleaned_symbol}'),
                        InlineKeyboardButton("☁️ سحابة إيشيموكو" if lang == 'ar' else "☁️ Ichimoku Cloud",
                                           callback_data=f'ichimoku_cloud_{cleaned_symbol}')
                    ]
                ]
                keyboard.extend(advanced_buttons)

            # إضافة زر العودة للقائمة الرئيسية
            keyboard.append([
                InlineKeyboardButton("🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu", callback_data='back_to_main')
            ])

            # التحقق من طول النص
            max_caption_length = 1000  # حد أقصى لطول التعليق في تلغرام

            # تقسيم التحليل إلى جزأين: الجزء الأول للرسم البياني والجزء الثاني للتفاصيل
            # تحديد نقطة التقسيم بعد قسم "الاتجاه العام"
            split_point = analysis_text.find("**مستويات الدعم والمقاومة**")
            if split_point == -1:
                split_point = analysis_text.find("**نظرة مستقبلية قصيرة المدى**")

            if split_point == -1 or split_point > max_caption_length - 100:
                # إذا لم يتم العثور على نقطة تقسيم مناسبة، استخدم التقسيم البسيط
                if len(analysis_text) > max_caption_length:
                    first_part = analysis_text[:max_caption_length - 100] + "..."
                    second_part = analysis_text
                else:
                    first_part = analysis_text
                    second_part = None
            else:
                # تقسيم التحليل عند نقطة التقسيم المناسبة
                first_part = analysis_text[:split_point].strip()
                second_part = analysis_text

            # إرسال الرسم البياني مع الجزء الأول من التحليل
            if 'chart_data' in market_data and market_data['chart_data']:
                try:
                    # استخدام البيانات مباشرة من الذاكرة
                    photo_data = market_data['chart_data']
                    logger.info(f"استخدام بيانات الرسم البياني من الذاكرة (حجم البيانات: {len(photo_data)} بايت)")

                    # إرسال الصورة مع الجزء الأول من التحليل
                    try:
                        sent_message = await wait_message.reply_photo(
                            photo=photo_data,
                            caption=first_part,
                            reply_markup=InlineKeyboardMarkup(keyboard),
                            parse_mode=ParseMode.MARKDOWN
                        )
                        logger.info(f"تم إرسال الرسم البياني بنجاح: message_id={sent_message.message_id}")

                        # إرسال الجزء الثاني من التحليل في رسالة منفصلة إذا كان موجوداً
                        if second_part and second_part != first_part:
                            await update.message.reply_text(
                                text=second_part,
                                reply_markup=InlineKeyboardMarkup(keyboard),
                                parse_mode=ParseMode.MARKDOWN
                            )
                            logger.info("تم إرسال الجزء الثاني من التحليل في رسالة منفصلة")

                        # حذف رسالة الانتظار
                        await wait_message.delete()
                    except Exception as send_error:
                        logger.error(f"خطأ في إرسال الصورة: {str(send_error)}")
                        # محاولة إرسال الصورة بدون نص
                        try:
                            sent_message = await wait_message.reply_photo(photo=photo_data)
                            logger.info("تم إرسال الصورة بدون نص")
                            # إرسال النص في رسالة منفصلة
                            await update.message.reply_text(
                                text=second_part or first_part,
                                reply_markup=InlineKeyboardMarkup(keyboard),
                                parse_mode=ParseMode.MARKDOWN
                            )
                        except Exception as bare_send_error:
                            logger.error(f"فشل إرسال الصورة بدون نص: {str(bare_send_error)}")
                            # إرسال النص فقط
                            await wait_message.edit_text(
                                text=first_part,
                                reply_markup=InlineKeyboardMarkup(keyboard),
                                parse_mode=ParseMode.MARKDOWN
                            )

                            # إرسال الجزء الثاني من التحليل في رسالة منفصلة إذا كان موجوداً
                            if second_part and second_part != first_part:
                                await update.message.reply_text(
                                    text=second_part,
                                    reply_markup=InlineKeyboardMarkup(keyboard),
                                    parse_mode=ParseMode.MARKDOWN
                                )
                except Exception as chart_error:
                    logger.error(f"خطأ في معالجة بيانات الرسم البياني: {str(chart_error)}")
                    # محاولة إنشاء رسم بياني بسيط
                    try:
                        logger.info("محاولة إنشاء رسم بياني بسيط...")
                        import matplotlib.pyplot as plt
                        import numpy as np

                        # إنشاء رسم بياني بسيط
                        plt.figure(figsize=(10, 6))
                        if 'df' in market_data and not market_data['df'].empty:
                            # استخدام البيانات الفعلية إذا كانت متاحة
                            plt.plot(market_data['df']['close'].values, 'b-', linewidth=2)
                        else:
                            # استخدام بيانات عشوائية إذا لم تكن البيانات الفعلية متاحة
                            plt.plot(np.random.rand(10) + 0.5 * np.arange(10), 'r-', linewidth=2)
                        plt.title(f"{cleaned_symbol} Chart")
                        plt.grid(True)

                        # حفظ الرسم البياني في الذاكرة
                        from io import BytesIO
                        simple_buffer = BytesIO()
                        plt.savefig(simple_buffer, format='jpeg', dpi=100)
                        plt.close()

                        # التحقق من البيانات
                        simple_buffer.seek(0)
                        simple_data = simple_buffer.getvalue()

                        if len(simple_data) > 0:
                            logger.info(f"تم إنشاء رسم بياني بسيط في الذاكرة (حجم البيانات: {len(simple_data)} بايت)")

                            # إرسال الرسم البياني البسيط مع الجزء الأول من التحليل
                            sent_message = await wait_message.reply_photo(
                                photo=simple_data,
                                caption=first_part,
                                reply_markup=InlineKeyboardMarkup(keyboard),
                                parse_mode=ParseMode.MARKDOWN
                            )

                            # إرسال الجزء الثاني من التحليل في رسالة منفصلة إذا كان موجوداً
                            if second_part and second_part != first_part:
                                await update.message.reply_text(
                                    text=second_part,
                                    reply_markup=InlineKeyboardMarkup(keyboard),
                                    parse_mode=ParseMode.MARKDOWN
                                )

                            # حذف رسالة الانتظار
                            await wait_message.delete()
                            return
                    except Exception as simple_chart_error:
                        logger.error(f"فشل إنشاء رسم بياني بسيط: {str(simple_chart_error)}")

                        # في حالة فشل كل المحاولات، تحديث رسالة الانتظار بالتحليل
                        await wait_message.edit_text(
                            text=first_part,
                            reply_markup=InlineKeyboardMarkup(keyboard),
                            parse_mode=ParseMode.MARKDOWN
                        )

                        # إرسال الجزء الثاني من التحليل في رسالة منفصلة إذا كان موجوداً
                        if second_part and second_part != first_part:
                            await update.message.reply_text(
                                text=second_part,
                                reply_markup=InlineKeyboardMarkup(keyboard),
                                parse_mode=ParseMode.MARKDOWN
                            )
                except Exception as chart_error:
                    logger.error(f"خطأ في معالجة الرسم البياني: {str(chart_error)}")
                    # في حالة حدوث خطأ، تحديث رسالة الانتظار بالتحليل
                    await wait_message.edit_text(
                        text=first_part,
                        reply_markup=InlineKeyboardMarkup(keyboard),
                        parse_mode=ParseMode.MARKDOWN
                    )

                    # إرسال الجزء الثاني من التحليل في رسالة منفصلة إذا كان موجوداً
                    if second_part and second_part != first_part:
                        await update.message.reply_text(
                            text=second_part,
                            reply_markup=InlineKeyboardMarkup(keyboard),
                            parse_mode=ParseMode.MARKDOWN
                        )
            else:
                logger.warning(f"لا توجد بيانات للرسم البياني في الذاكرة: {market_data.get('chart_data') is None}")

                # محاولة إنشاء رسم بياني بسيط إذا كان DataFrame متاحًا
                try:
                    if 'df' in market_data and not market_data['df'].empty:
                        logger.info("محاولة إنشاء رسم بياني بسيط من DataFrame المتاح في الذاكرة...")
                        import matplotlib.pyplot as plt
                        import numpy as np
                        from io import BytesIO

                        # إنشاء رسم بياني بنمط الشموع مع تحسينات
                        plt.figure(figsize=(10, 6), facecolor='#1E1E1E')
                        ax = plt.gca()
                        ax.set_facecolor('#121212')

                        # استخراج البيانات اللازمة للشموع
                        if all(col in market_data['df'].columns for col in ['open', 'high', 'low', 'close']):
                            # إنشاء مصفوفة للشموع
                            from matplotlib.dates import date2num
                            import matplotlib.dates as mdates
                            import pandas as pd

                            # تحويل الفهرس إلى تاريخ إذا لم يكن كذلك بالفعل
                            if not isinstance(market_data['df'].index, pd.DatetimeIndex):
                                # استخدام الفهرس الرقمي
                                dates = np.arange(len(market_data['df']))
                            else:
                                # استخدام التواريخ الفعلية
                                dates = date2num(market_data['df'].index.to_pydatetime())

                            # إنشاء مصفوفة OHLC
                            df = market_data['df']
                            last_n_candles = 50  # عدد الشموع التي سيتم عرضها

                            # التأكد من أن لدينا بيانات كافية
                            if len(df) > last_n_candles:
                                df_display = df.iloc[-last_n_candles:]
                            else:
                                df_display = df

                            ohlc = np.column_stack((
                                dates[-len(df_display):],
                                df_display['open'].values,
                                df_display['high'].values,
                                df_display['low'].values,
                                df_display['close'].values,
                            ))

                            # رسم الشموع
                            from mplfinance.original_flavor import candlestick_ohlc
                            candlestick_ohlc(ax, ohlc, width=0.6, colorup='#00C805', colordown='#FF3232')

                            # إضافة المتوسطات المتحركة
                            if 'close' in df.columns and len(df) >= 20:
                                # حساب المتوسطات المتحركة
                                ema20 = df['close'].ewm(span=20, adjust=False).mean()
                                ema50 = df['close'].ewm(span=50, adjust=False).mean()
                                ema200 = df['close'].ewm(span=200, adjust=False).mean() if len(df) >= 200 else None

                                # رسم المتوسطات المتحركة
                                if len(df) >= 20:
                                    plt.plot(dates[-len(df_display):], ema20.values[-len(df_display):],
                                             color='#3CB9FF', linewidth=1.5, label='EMA20')

                                if len(df) >= 50:
                                    plt.plot(dates[-len(df_display):], ema50.values[-len(df_display):],
                                             color='#FF9900', linewidth=1.5, label='EMA50')

                                if ema200 is not None and len(df) >= 200:
                                    plt.plot(dates[-len(df_display):], ema200.values[-len(df_display):],
                                             color='#FF00FF', linewidth=1.5, label='EMA200')

                                plt.legend(loc='upper left', facecolor='#1E1E1E', edgecolor='#444444',
                                           labelcolor='white')

                            # تنسيق المحور السيني
                            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
                            plt.xticks(rotation=45, color='white')
                            plt.yticks(color='white')
                        else:
                            # إذا لم تكن البيانات متاحة، استخدم الرسم البياني الخطي
                            plt.plot(market_data['df']['close'].values, color='#3CB9FF', linewidth=2)

                        # تحسين العنوان والشبكة
                        plt.title(f"{cleaned_symbol} Chart", color='white', fontsize=14, fontweight='bold')
                        plt.grid(True, linestyle='--', alpha=0.3)

                        # إضافة حجم التداول في الجزء السفلي
                        if 'volume' in df.columns and len(df) > 0:
                            # إنشاء محور فرعي للحجم
                            volume_ax = plt.axes([0.125, 0.05, 0.775, 0.1], facecolor='#121212')
                            volume_ax.set_title('Volume', color='white', fontsize=10)
                            volume_ax.bar(dates[-len(df_display):], df_display['volume'].values, color='#3CB9FF', alpha=0.5)
                            volume_ax.set_xticks([])  # إخفاء علامات المحور السيني
                            volume_ax.tick_params(axis='y', colors='white')
                            volume_ax.spines['bottom'].set_color('#444444')
                            volume_ax.spines['top'].set_color('#444444')
                            volume_ax.spines['left'].set_color('#444444')
                            volume_ax.spines['right'].set_color('#444444')

                        # تحسين حدود الرسم البياني
                        for spine in ax.spines.values():
                            spine.set_color('#444444')

                        # إضافة معلومات السعر الحالي
                        if 'close' in market_data['df'].columns and len(market_data['df']) > 0:
                            current_price = market_data['df']['close'].iloc[-1]
                            plt.axhline(y=current_price, color='#FFFF00', linestyle='--', alpha=0.5)
                            plt.text(0.02, 0.95, f"السعر الحالي: {current_price:.2f}",
                                    transform=ax.transAxes, color='#FFFF00', fontsize=10)

                            # إضافة الوقت والتاريخ للرسم البياني
                            import datetime
                            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            plt.text(0.02, 0.02, f"تاريخ التحليل: {current_time}",
                                    transform=ax.transAxes, color='white', fontsize=8)

                        # حفظ الرسم البياني في الذاكرة بدقة أعلى وتنسيق PNG للجودة
                        chart_buffer = BytesIO()
                        plt.savefig(chart_buffer, format='png', dpi=150, bbox_inches='tight')
                        plt.close()

                        # التحقق من البيانات
                        chart_buffer.seek(0)
                        chart_data = chart_buffer.getvalue()

                        # التحقق من حجم البيانات وضغطها إذا كانت كبيرة جدًا
                        if len(chart_data) > 5000000:  # أكبر من 5 ميجابايت
                            logger.info(f"حجم الرسم البياني كبير ({len(chart_data)} بايت)، جاري الضغط...")
                            try:
                                # إعادة إنشاء الصورة بدقة أقل
                                from PIL import Image
                                from io import BytesIO

                                # تحويل البيانات إلى صورة
                                img_buffer = BytesIO(chart_data)
                                img = Image.open(img_buffer)
                                img = img.convert('RGB')  # تحويل إلى RGB لتقليل الحجم

                                # ضغط الصورة
                                compressed_buffer = BytesIO()
                                img.save(compressed_buffer, format='JPEG', quality=85)
                                img.close()

                                # استخدام النسخة المضغوطة
                                compressed_buffer.seek(0)
                                compressed_data = compressed_buffer.getvalue()

                                if len(compressed_data) < len(chart_data):
                                    chart_data = compressed_data
                                    logger.info(f"تم ضغط الرسم البياني من {len(chart_data)} إلى {len(compressed_data)} بايت")
                            except Exception as compress_error:
                                logger.error(f"خطأ في ضغط الرسم البياني: {str(compress_error)}")

                        if len(chart_data) > 0:
                            logger.info(f"تم إنشاء رسم بياني بسيط من DataFrame في الذاكرة (حجم البيانات: {len(chart_data)} بايت)")
                            simple_data = chart_data

                            # إرسال الرسم البياني مع الجزء الأول من التحليل
                            try:
                                sent_message = await wait_message.reply_photo(
                                    photo=simple_data,
                                    caption=first_part,
                                    reply_markup=InlineKeyboardMarkup(keyboard),
                                    parse_mode=ParseMode.MARKDOWN
                                )
                                logger.info(f"تم إرسال الرسم البياني بنجاح: message_id={sent_message.message_id}")

                                # إرسال الجزء الثاني من التحليل في رسالة منفصلة إذا كان موجوداً
                                if second_part and second_part != first_part:
                                    second_message = await update.message.reply_text(
                                        text=second_part,
                                        reply_markup=InlineKeyboardMarkup(keyboard),
                                        parse_mode=ParseMode.MARKDOWN
                                    )
                                    logger.info(f"تم إرسال الجزء الثاني من التحليل بنجاح: message_id={second_message.message_id}")
                            except Exception as send_error:
                                logger.error(f"خطأ في إرسال الرسم البياني: {str(send_error)}")
                                # محاولة إرسال الصورة بدون نص
                                try:
                                    sent_message = await wait_message.reply_photo(photo=simple_data)
                                    logger.info("تم إرسال الصورة بدون نص")
                                    # إرسال النص في رسالة منفصلة
                                    await update.message.reply_text(
                                        text=analysis_text,
                                        reply_markup=InlineKeyboardMarkup(keyboard),
                                        parse_mode=ParseMode.MARKDOWN
                                    )
                                except Exception as bare_send_error:
                                    logger.error(f"فشل إرسال الصورة بدون نص: {str(bare_send_error)}")
                                    # إرسال النص فقط
                                    await wait_message.edit_text(
                                        text="⚠️ تعذر إرسال الرسم البياني. إليك التحليل النصي:\n\n" + analysis_text,
                                        reply_markup=InlineKeyboardMarkup(keyboard),
                                        parse_mode=ParseMode.MARKDOWN
                                    )

                            # حذف رسالة الانتظار
                            await wait_message.delete()
                            return
                    else:
                        logger.warning("لا يوجد DataFrame متاح لإنشاء رسم بياني بسيط")
                except Exception as simple_chart_error:
                    logger.error(f"فشل إنشاء رسم بياني بسيط من DataFrame: {str(simple_chart_error)}")

                # تحديث رسالة الانتظار بالتحليل إذا فشلت كل المحاولات
                # إرسال التحليل بدون parse_mode لتجنب أخطاء Telegram مع الكيانات الخاصة
                await wait_message.edit_text(
                    text=first_part,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )

                # إرسال الجزء الثاني من التحليل في رسالة منفصلة إذا كان موجوداً
                if second_part and second_part != first_part:
                    # إرسال الجزء الثاني من التحليل بدون parse_mode
                    await update.message.reply_text(
                        text=second_part,
                        reply_markup=InlineKeyboardMarkup(keyboard)
                    )

        except Exception as e:
            logger.error(f"Error analyzing symbol: {str(e)}", exc_info=True)
            error_text = (
                f"Error analyzing {cleaned_symbol}: {str(e)}" if lang == 'en' else
                f"خطأ في تحليل {cleaned_symbol}: {str(e)}"
            )
            await wait_message.edit_text(error_text)

    except Exception as e:
        logger.error(f"Error in analyze_symbol: {str(e)}", exc_info=True)
        error_text = (
            "An error occurred during analysis. Please try again." if lang == 'en' else
            "حدث خطأ أثناء التحليل. الرجاء المحاولة مرة أخرى."
        )
        await update.message.reply_text(error_text)

async def help_command(update: Update, context: CallbackContext):
    """عرض رسالة المساعدة"""
    user_id = str(update.effective_user.id)
    settings = subscription_system.get_user_settings(user_id)
    lang = settings.get('lang', 'ar')

    help_text = get_text('help_text', lang)
    keyboard = [[InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(help_text, reply_markup=reply_markup, parse_mode=ParseMode.MARKDOWN)

async def alert_command(update: Update, context: CallbackContext):
    """إعداد تنبيه سعر"""
    user_id = str(update.message.from_user.id)
    lang = context.bot_data.get('user_settings', {}).get(user_id, {}).get('language', 'ar')

    keyboard = [[InlineKeyboardButton(get_text('back', lang), callback_data='back_to_main')]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    user_states[update.message.from_user.id] = 'waiting_for_alert_symbol'

    messages = {
        'ar': 'الرجاء إدخال رمز العملة للتنبيه (مثال: BTC/USDT)',
        'en': 'Please enter the currency pair for alert (example: BTC/USDT)'
    }

    await update.message.reply_text(
        messages[lang],
        reply_markup=reply_markup
    )

async def handle_message(update: Update, context: CallbackContext):
    """معالجة الرسائل النصية"""
    try:
        user_id = str(update.effective_user.id)
        message_text = update.message.text.strip()
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # التحقق من حظر المستخدم
        banned_ref = db.collection('banned_users').document(user_id).get()
        if banned_ref.exists and banned_ref.to_dict().get('status') == 'banned':
            logger.info(f"محاولة وصول من مستخدم محظور: {user_id}")
            await update.message.reply_text("⛔️ عذراً، تم حظر حسابك من استخدام البوت. يرجى التواصل مع المطور إذا كنت تعتقد أن هذا خطأ.")
            return

        # التحقق من حالة المستخدم للدردشة مع الذكاء الاصطناعي
        if user_id in user_states and isinstance(user_states[user_id], dict) and user_states[user_id].get('state') == 'ai_chat':
            # التحقق من حالة الاشتراك ووجود مفتاح Gemini API
            if not subscription_system.is_subscribed(user_id):
                await update.message.reply_text(
                    "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                    "This feature is available for subscribers only"
                )
                user_states.pop(user_id, None)
                return

            has_gemini_api = await api_manager.has_api_keys(user_id, 'gemini')
            if not has_gemini_api:
                await update.message.reply_text(
                    "يرجى إضافة مفتاح Gemini API الخاص بك أولاً" if lang == 'ar' else
                    "Please add your Gemini API key first"
                )
                user_states.pop(user_id, None)
                return

            # إرسال رسالة انتظار
            wait_message = await update.message.reply_text(
                "جاري التفكير..." if lang == 'ar' else
                "Thinking..."
            )

            try:
                # استدعاء وظيفة الدردشة مع الذكاء الاصطناعي
                from ai_chat import chat_with_ai
                response = await chat_with_ai(user_id, message_text, lang)

                if response:
                    # إرسال رد الذكاء الاصطناعي
                    keyboard = [[InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]]
                    await wait_message.edit_text(
                        text=response,
                        reply_markup=InlineKeyboardMarkup(keyboard)
                    )
                else:
                    await wait_message.edit_text(
                        "عذراً، حدث خطأ أثناء الاتصال بالذكاء الاصطناعي. يرجى المحاولة مرة أخرى." if lang == 'ar' else
                        "Sorry, an error occurred while connecting to AI. Please try again."
                    )
            except Exception as ai_error:
                logger.error(f"خطأ في الدردشة مع الذكاء الاصطناعي: {str(ai_error)}")
                await wait_message.edit_text(
                    "عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى." if lang == 'ar' else
                    "Sorry, an error occurred while processing your request. Please try again."
                )

            # الاستمرار في حالة الدردشة
            return

        # تهيئة الإعدادات إذا لم تكن موجودة
        if user_id not in user_settings:
            user_settings[user_id] = {
                'lang': 'ar',
                'currencies': [],
                'indicators': []
            }

        settings = user_settings[user_id]
        lang = settings.get('lang', 'ar')

        # التحقق من حالة المستخدم
        if user_id in user_states:
            state_data = user_states[user_id]
            if isinstance(state_data, dict):
                state = state_data.get('state')

                # معالجة إدخال مفاتيح API
                if 'api_setup_state' in state_data:
                    api_setup_state = state_data['api_setup_state']

                    if api_setup_state == 'binance_key':
                        # حفظ مفتاح Binance API
                        await api_manager.save_api_key(user_id, 'binance', message_text)

                        # حذف رسالة المستخدم التي تحتوي على مفتاح API
                        try:
                            await update.message.delete()
                            logger.info(f"تم حذف رسالة مفتاح Binance API للمستخدم {user_id}")
                        except Exception as e:
                            logger.error(f"خطأ في حذف رسالة مفتاح Binance API: {str(e)}")

                        # طلب السر
                        await update.message.reply_text(
                            "تم حفظ مفتاح API بنجاح. الآن، أدخل سر API (API Secret):" if lang == 'ar' else
                            "API Key saved successfully. Now, enter your API Secret:"
                        )

                        # تحديث الحالة
                        user_states[user_id] = {'api_setup_state': 'binance_secret'}
                        return

                    elif api_setup_state == 'binance_secret':
                        # الحصول على المفتاح المخزن مسبقاً
                        api_key, _ = await api_manager.get_api_keys(user_id, 'binance')

                        # حذف رسالة المستخدم التي تحتوي على سر API
                        try:
                            await update.message.delete()
                            logger.info(f"تم حذف رسالة سر Binance API للمستخدم {user_id}")
                        except Exception as e:
                            logger.error(f"خطأ في حذف رسالة سر Binance API: {str(e)}")

                        if api_key:
                            # حفظ سر Binance API
                            await api_manager.save_api_key(user_id, 'binance', api_key, message_text)

                            # التحقق من صحة المفاتيح
                            is_valid, error_message = await verify_binance_api(api_key, message_text)

                            if is_valid:
                                await update.message.reply_text(
                                    "✅ تم إعداد Binance API بنجاح! يمكنك الآن استخدام البوت بكامل إمكانياته." if lang == 'ar' else
                                    "✅ Binance API setup successful! You can now use the bot with full capabilities."
                                )
                            else:
                                await update.message.reply_text(
                                    f"❌ مفاتيح API غير صالحة. {error_message if error_message else 'يرجى التحقق والمحاولة مرة أخرى.'}" if lang == 'ar' else
                                    f"❌ API keys are invalid. {error_message if error_message else 'Please check and try again.'}"
                                )
                        else:
                            await update.message.reply_text(
                                "❌ حدث خطأ في استرجاع مفتاح API. الرجاء المحاولة مرة أخرى." if lang == 'ar' else
                                "❌ Error retrieving API key. Please try again."
                            )

                        # إعادة تعيين الحالة
                        del user_states[user_id]
                        return

                    elif api_setup_state in ['gemini_key', 'kucoin_key', 'coinbase_key', 'bybit_key', 'okx_key', 'kraken_key']:
                        # استخراج نوع المنصة من حالة الإعداد
                        platform = api_setup_state.replace('_key', '')

                        # حفظ مفتاح API
                        await api_manager.save_api_key(user_id, platform, message_text)

                        # حذف رسالة المستخدم التي تحتوي على مفتاح API
                        try:
                            await update.message.delete()
                            logger.info(f"تم حذف رسالة مفتاح {platform} API للمستخدم {user_id}")
                        except Exception as e:
                            logger.error(f"خطأ في حذف رسالة مفتاح {platform} API: {str(e)}")

                        # التعامل مع المنصات التي تتطلب سر API
                        if platform in ['kucoin', 'coinbase', 'bybit', 'okx', 'kraken']:
                            # طلب السر
                            await update.message.reply_text(
                                f"تم حفظ مفتاح {platform.capitalize()} API بنجاح. الآن، أدخل سر API (API Secret):" if lang == 'ar' else
                                f"{platform.capitalize()} API Key saved successfully. Now, enter your API Secret:"
                            )

                            # تحديث الحالة
                            user_states[user_id] = {'api_setup_state': f'{platform}_secret'}
                            return

                        # للمنصات التي لا تتطلب سر API (مثل Gemini)
                        if platform == 'gemini':
                            # التحقق من صحة المفتاح
                            is_valid, error_message = await verify_gemini_api(message_text)

                            if is_valid:
                                await update.message.reply_text(
                                    "✅ تم إعداد Gemini API بنجاح! يمكنك الآن استخدام ميزات التحليل المتقدم." if lang == 'ar' else
                                    "✅ Gemini API setup successful! You can now use advanced analysis features."
                                )
                            else:
                                await update.message.reply_text(
                                    f"❌ مفتاح API غير صالح. {error_message if error_message else 'يرجى التحقق والمحاولة مرة أخرى.'}" if lang == 'ar' else
                                    f"❌ API key is invalid. {error_message if error_message else 'Please check and try again.'}"
                                )
                        else:
                            # للمنصات الأخرى التي لا تتطلب تحقق خاص
                            await update.message.reply_text(
                                f"✅ تم إعداد {platform.capitalize()} API بنجاح!" if lang == 'ar' else
                                f"✅ {platform.capitalize()} API setup successful!"
                            )

                        # إعادة تعيين الحالة
                        del user_states[user_id]
                        return

                    elif api_setup_state in ['kucoin_secret', 'coinbase_secret', 'bybit_secret', 'okx_secret', 'kraken_secret']:
                        # استخراج نوع المنصة من حالة الإعداد
                        platform = api_setup_state.replace('_secret', '')

                        # الحصول على المفتاح المخزن مسبقاً
                        api_key, _ = await api_manager.get_api_keys(user_id, platform)

                        # حذف رسالة المستخدم التي تحتوي على سر API
                        try:
                            await update.message.delete()
                            logger.info(f"تم حذف رسالة سر {platform} API للمستخدم {user_id}")
                        except Exception as e:
                            logger.error(f"خطأ في حذف رسالة سر {platform} API: {str(e)}")

                        if api_key:
                            # حفظ سر API
                            await api_manager.save_api_key(user_id, platform, api_key, message_text)

                            # رسالة نجاح
                            await update.message.reply_text(
                                f"✅ تم إعداد {platform.capitalize()} API بنجاح!" if lang == 'ar' else
                                f"✅ {platform.capitalize()} API setup successful!"
                            )
                        else:
                            await update.message.reply_text(
                                "❌ حدث خطأ في استرجاع مفتاح API. الرجاء المحاولة مرة أخرى." if lang == 'ar' else
                                "❌ Error retrieving API key. Please try again."
                            )

                        # إعادة تعيين الحالة
                        del user_states[user_id]
                        return




            else:
                state = state_data

            if state == 'waiting_for_custom_alert':
                await process_custom_alert(update, context)
                return

            elif state == 'waiting_for_custom_interval':
                # تم إزالة معالجة إدخال فترة مخصصة للتقارير الدورية
                # إعادة المستخدم إلى القائمة الرئيسية
                await update.message.reply_text(
                    "تم إلغاء هذه الميزة" if lang == 'ar' else "This feature has been removed",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]
                    ])
                )
                # مسح حالة المستخدم
                if user_id in user_states:
                    del user_states[user_id]
                return

            elif state == 'waiting_for_symbol':
                # تنظيف رمز العملة
                symbol = message_text.upper().replace('/', '')
                # تحليل العملة
                await analyze_symbol(update, context, symbol)
                return



        # التحقق من رمز العملة (لرسائل خارج سياق الحالات)
        # إذا كان نص الرسالة يبدو مثل رمز عملة، سنحاول تحليله مباشرة
        # تحسين التعبير المنتظم ليكون أكثر مرونة
        if re.match(r'^[A-Za-z0-9/\-_.]{1,15}$', message_text):
            # إزالة الرموز الخاصة وتحويل إلى الأحرف الكبيرة
            symbol = re.sub(r'[^A-Za-z0-9]', '', message_text.upper())

            # التحقق من وجود حروف في الرمز
            if any(c.isalpha() for c in symbol):
                loading_message = await update.message.reply_text(
                    get_text('analyzing_market', lang)
                )

                # إرسال رمز العملة لتحليله
                await analyze_symbol(update, context, symbol, loading_message)
                return

        # إذا لم يكن هناك حالة خاصة، نعرض القائمة الرئيسية
        await show_main_menu(update, context, new_message=True)

    except Exception as e:
        logger.error(f"Error handling message: {str(e)}")
        error_text = "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else "Sorry, an error occurred. Please try again"
        await update.message.reply_text(error_text)

def load_alerts():
    """تحميل التنبيهات من Firestore"""
    global price_alerts
    try:
        alerts_ref = db.collection('alerts')
        alerts_docs = alerts_ref.get()
        price_alerts = {}
        for doc in alerts_docs:
            price_alerts[doc.id] = doc.to_dict()
    except Exception as e:
        logger.error(f"Error loading alerts: {str(e)}")
        price_alerts = {}
    return price_alerts

def save_alerts():
    """حفظ التنبيهات في Firestore"""
    global price_alerts
    try:
        batch = db.batch()
        for user_id, alerts in price_alerts.items():
            alert_ref = db.collection('alerts').document(user_id)
            batch.set(alert_ref, alerts)
        batch.commit()
    except Exception as e:
        logger.error(f"Error saving alerts: {str(e)}")

def save_user_settings(user_id: str, **settings):
    """حفظ إعدادات المستخدم في Firestore"""
    try:
        settings_ref = db.collection('user_settings').document(user_id)
        settings_ref.set(settings, merge=True)
        return True
    except Exception as e:
        logger.error(f"Error saving user settings: {str(e)}")
        return False

def load_user_settings(user_id: str):
    """تحميل إعدادات المستخدم من Firestore"""
    try:
        settings_ref = db.collection('user_settings').document(user_id)
        settings_doc = settings_ref.get()

        if settings_doc.exists:
            settings = settings_doc.to_dict()
        else:
            settings = {
                'indicators': [],
                'currencies': [],
                'lang': 'ar'  # اللغة الافتراضية هي العربية
            }
            settings_ref.set(settings)

        # التأكد من وجود جميع المفاتيح الضرورية
        if 'lang' not in settings:
            settings['lang'] = 'ar'
            settings_ref.update({'lang': 'ar'})

        return settings
    except Exception as e:
        logger.error(f"Error loading user settings: {str(e)}")
        return {'indicators': [], 'currencies': [], 'lang': 'ar'}

async def check_alerts(context: CallbackContext):
    """التحقق من التنبيهات وإرسال الإشعارات"""
    try:
        alerts_ref = db.collection('alerts')
        alerts = alerts_ref.get()
        for alert_doc in alerts:
            # تجاهل وثائق البيانات الوصفية
            if alert_doc.id == '_metadata' or alert_doc.id == '_init':
                continue

            user_id = alert_doc.id
            user_alerts = alert_doc.to_dict()

            # التعديل هنا: استخدام list() لتكرار نسخة من العناصر لتجنب خطأ تغيير حجم القاموس أثناء التكرار
            for symbol, symbol_alerts in list(user_alerts.items()):
                # التحقق من صحة رمز العملة
                if not isinstance(symbol, str) or symbol.startswith('_'):
                    continue

                try:
                    market_data = await ca.get_market_data(symbol, user_id=user_id)
                    if market_data:
                        current_price = market_data['price']
                        triggered_alerts = []

                        for alert in symbol_alerts:
                            price = alert['price']
                            condition = alert['condition']

                            if (condition == 'above' and current_price >= price) or \
                               (condition == 'below' and current_price <= price):
                                triggered_alerts.append(alert)

                        for alert in triggered_alerts:
                            condition_text = "أعلى من" if alert['condition'] == 'above' else "أقل من"
                            alert_text = (
                                f"🔔 تنبيه!\n\n"
                                f"💱 {symbol}\n"
                                f"💰 السعر الحالي: {current_price:.2f}\n"
                                f"⚠️ {condition_text} {alert['price']:.2f}"
                            )

                            try:
                                await context.bot.send_message(
                                    chat_id=user_id,
                                    text=alert_text
                                )
                                # حذف التنبيه المنفذ
                                symbol_alerts.remove(alert)

                            except Exception as e:
                                logger.error(f"Error sending alert to user {user_id}: {str(e)}")

                        # تحديث التنبيهات في Firestore
                        if triggered_alerts:
                            if symbol_alerts:
                                user_alerts[symbol] = symbol_alerts
                            else:
                                del user_alerts[symbol]

                            if user_alerts:
                                alerts_ref.document(user_id).set(user_alerts)
                            else:
                                alerts_ref.document(user_id).delete()

                except Exception as e:
                    logger.error(f"Error checking price for {symbol}: {str(e)}")
                    continue
    except Exception as e:
        logger.error(f"Error in check_alerts: {str(e)}")

# تم إزالة وظيفة إرسال التقارير الدورية

async def verify_payment_transaction(user_id: str, transaction_id: str, lang: str = 'ar') -> bool:
    """التحقق من عملية الدفع وإضافة الاشتراك"""
    try:
        # التحقق من وجود المعاملة في Firestore
        transaction_ref = db.collection('transactions').document(transaction_id)
        transaction_data = transaction_ref.get()

        if not transaction_data.exists:
            logger.error(f"Transaction {transaction_id} not found")
            return False

        transaction = transaction_data.to_dict()

        # التحقق من أن المعاملة لم يتم استخدامها من قبل
        if transaction.get('used', False):
            logger.error(f"Transaction {transaction_id} already used")
            return False

        # التحقق من معرف المستخدم
        if transaction.get('user_id') != user_id:
            logger.error(f"Transaction {transaction_id} belongs to different user")
            return False

        # التحقق من المبلغ
        if transaction.get('amount') != 5:
            logger.error(f"Invalid amount for transaction {transaction_id}")
            return False

        # تحديث حالة المعاملة
        transaction_ref.update({
            'used': True,
            'activation_date': datetime.now().isoformat()
        })

        # إضافة الاشتراك
        subscription_system = SubscriptionSystem()
        return subscription_system.add_subscription(user_id, lang, transaction_id)

    except Exception as e:
        logger.error(f"Error verifying payment: {str(e)}")
        return False

async def create_payment_transaction(user_id: str, binance_txid: str = None) -> str:
    """إنشاء معاملة دفع جديدة"""
    try:
        # حذف المعاملات المعلقة القديمة للمستخدم
        transactions_ref = db.collection('transactions')
        old_transactions = transactions_ref.where(filter=FieldFilter('user_id', '==', user_id)).where(filter=FieldFilter('status', '==', 'pending')).get()

        for old_tx in old_transactions:
            # حذف المعاملات المعلقة التي مر عليها أكثر من ساعة
            created_at = datetime.fromisoformat(old_tx.to_dict().get('created_at', '2020-01-01'))
            if datetime.now() - created_at > timedelta(hours=1):
                old_tx.reference.delete()
                logger.info(f"تم حذف معاملة معلقة قديمة: {old_tx.id}")

        transaction_data = {
            'user_id': user_id,
            'amount': 5.0,  # USDT
            'created_at': datetime.now().isoformat(),
            'status': 'pending',
            'used': False,
            'expires_at': (datetime.now() + timedelta(hours=1)).isoformat()  # تنتهي بعد ساعة
        }

        if binance_txid:
            # استخدام معرف المعاملة من Binance
            transaction_ref = transactions_ref.document(binance_txid)
            transaction_data['binance_txid'] = binance_txid
        else:
            # إنشاء معرف مؤقت
            transaction_ref = transactions_ref.document()

        transaction_ref.set(transaction_data)
        return transaction_ref.id

    except Exception as e:
        logger.error(f"خطأ في إنشاء معاملة دفع: {str(e)}")
        return None

async def update_transaction_with_binance_id(temp_txid: str, binance_txid: str) -> bool:
    """تحديث معرف المعاملة المؤقت بمعرف Binance الفعلي"""
    try:
        # نسخ بيانات المعاملة المؤقتة
        temp_ref = db.collection('transactions').document(temp_txid)
        temp_data = temp_ref.get()

        if temp_data.exists:
            transaction_data = temp_data.to_dict()

            # إنشاء وثيقة جديدة بمعرف Binance
            new_ref = db.collection('transactions').document(binance_txid)
            transaction_data['binance_txid'] = binance_txid
            new_ref.set(transaction_data)

            # حذف المعاملة المؤقتة
            temp_ref.delete()

            logger.info(f"Transaction ID updated from {temp_txid} to {binance_txid}")
            return True

        return False
    except Exception as e:
        logger.error(f"Error updating transaction ID: {str(e)}")

async def cleanup_pending_transactions(context: CallbackContext):
    """تنظيف المعاملات المعلقة القديمة"""
    try:
        logger.info(f"⏰ بدء عملية التنظيف الدوري في {datetime.now().isoformat()}")
        transactions_ref = db.collection('transactions')
        pending_transactions = transactions_ref.where(filter=FieldFilter('status', '==', 'pending')).get()

        count = 0
        for tx in pending_transactions:
            tx_data = tx.to_dict()
            created_at = datetime.fromisoformat(tx_data.get('created_at', '2020-01-01'))

            # حذف المعاملات المعلقة التي مر عليها أكثر من ساعة
            if datetime.now() - created_at > timedelta(hours=1):
                tx.reference.delete()
                count += 1

        if count > 0:
            logger.info(f"🗑️ تم حذف {count} من المعاملات المعلقة")
        else:
            logger.info(f"⚠️ لا توجد معاملات معلقة للحذف")

        logger.info(f"⏰ انتهاء عملية التنظيف الدوري في {datetime.now().isoformat()}")
    except Exception as e:
        logger.error(f"❌ خطأ في تنظيف المعاملات المعلقة: {str(e)}")

async def notify_expiring_transactions(context: CallbackContext):
    """إرسال إشعارات للمستخدمين قبل انتهاء صلاحية معاملاتهم"""
    try:
        logger.info(f"⏰ بدء عملية إرسال إشعارات المعاملات المنتهية في {datetime.now().isoformat()}")
        transactions_ref = db.collection('transactions')

        # الحصول على المعاملات المعلقة التي ستنتهي خلال الساعة القادمة
        now = datetime.now()
        one_hour_later = now + timedelta(minutes=30)  # إشعار قبل 30 دقيقة من انتهاء الصلاحية

        # البحث عن المعاملات المعلقة التي ستنتهي قريباً
        pending_transactions = transactions_ref.where(
            filter=FieldFilter('status', '==', 'pending')
        ).get()

        notification_count = 0
        for tx in pending_transactions:
            tx_data = tx.to_dict()
            tx_id = tx.id
            user_id = tx_data.get('user_id')

            # التحقق من وقت انتهاء الصلاحية
            if 'expires_at' in tx_data:
                expires_at = datetime.fromisoformat(tx_data.get('expires_at'))

                # إذا كانت المعاملة ستنتهي خلال الـ 30 دقيقة القادمة
                if now < expires_at <= one_hour_later:
                    # التحقق من أن المستخدم لم يتلق إشعاراً بالفعل
                    if not tx_data.get('notification_sent', False):
                        # إرسال إشعار للمستخدم
                        await send_transaction_expiry_notification(context, user_id, tx_id, expires_at)

                        # تحديث حالة الإشعار
                        tx.reference.update({
                            'notification_sent': True,
                            'notification_time': now.isoformat()
                        })

                        notification_count += 1

        if notification_count > 0:
            logger.info(f"📩 تم إرسال {notification_count} إشعار للمستخدمين حول المعاملات المنتهية قريباً")
        else:
            logger.info("ℹ️ لا توجد معاملات ستنتهي قريباً لإرسال إشعارات")

    except Exception as e:
        logger.error(f"❌ خطأ في إرسال إشعارات المعاملات المنتهية: {str(e)}")

async def send_transaction_expiry_notification(context: ContextTypes.DEFAULT_TYPE, user_id: str, transaction_id: str, expires_at: datetime):
    """إرسال إشعار للمستخدم قبل انتهاء صلاحية المعاملة"""
    try:
        # حساب الوقت المتبقي
        now = datetime.now()
        time_left = expires_at - now
        minutes_left = int(time_left.total_seconds() / 60)

        # إنشاء رسالة الإشعار
        message = f"⚠️ *تنبيه*: لديك معاملة دفع معلقة ستنتهي خلال {minutes_left} دقيقة.\n\n"
        message += "يمكنك تمديد صلاحية المعاملة أو إكمال عملية الدفع من خلال الضغط على الزر أدناه."

        # إنشاء أزرار التحكم
        keyboard = [
            [
                InlineKeyboardButton("🔄 تمديد الصلاحية", callback_data=f"extend_transaction_{transaction_id}")
            ],
            [
                InlineKeyboardButton("💳 إكمال الدفع", callback_data=f"complete_payment_{transaction_id}")
            ]
        ]

        # إرسال الإشعار
        await context.bot.send_message(
            chat_id=user_id,
            text=message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

        logger.info(f"📩 تم إرسال إشعار انتهاء صلاحية المعاملة للمستخدم {user_id} للمعاملة {transaction_id}")

    except Exception as e:
        logger.error(f"❌ خطأ في إرسال إشعار انتهاء صلاحية المعاملة للمستخدم {user_id}: {str(e)}")

async def extend_transaction(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """تمديد صلاحية معاملة معلقة"""
    try:
        # الحصول على معرف المعاملة من بيانات الاستدعاء
        callback_data = update.callback_query.data
        transaction_id = callback_data.replace("extend_transaction_", "")

        # الحصول على معرف المستخدم
        user_id = str(update.effective_user.id)

        # الحصول على المعاملة من قاعدة البيانات
        transaction_ref = db.collection('transactions').document(transaction_id)
        transaction_doc = transaction_ref.get()

        if not transaction_doc.exists:
            await update.callback_query.answer("❌ المعاملة غير موجودة", show_alert=True)
            return

        transaction_data = transaction_doc.to_dict()

        # التحقق من أن المعاملة تخص المستخدم الحالي
        if transaction_data.get('user_id') != user_id:
            await update.callback_query.answer("❌ ليس لديك صلاحية لتمديد هذه المعاملة", show_alert=True)
            return

        # التحقق من حالة المعاملة
        if transaction_data.get('status') != 'pending':
            await update.callback_query.answer("❌ لا يمكن تمديد المعاملة لأنها ليست معلقة", show_alert=True)
            return

        # تمديد صلاحية المعاملة لمدة ساعة إضافية
        new_expiry = datetime.now() + timedelta(hours=1)
        transaction_ref.update({
            'expires_at': new_expiry.isoformat(),
            'notification_sent': False  # إعادة تعيين حالة الإشعار
        })

        # إرسال تأكيد للمستخدم
        await update.callback_query.answer("✅ تم تمديد صلاحية المعاملة لمدة ساعة إضافية", show_alert=True)

        # تحديث الرسالة
        message = f"✅ تم تمديد صلاحية المعاملة بنجاح حتى {new_expiry.strftime('%H:%M:%S')}.\n\n"
        message += "يمكنك الآن إكمال عملية الدفع من خلال الضغط على الزر أدناه."

        keyboard = [
            [
                InlineKeyboardButton("💳 إكمال الدفع", callback_data=f"complete_payment_{transaction_id}")
            ]
        ]

        await update.callback_query.message.edit_text(
            text=message,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        logger.info(f"🔄 تم تمديد صلاحية المعاملة {transaction_id} للمستخدم {user_id}")

    except Exception as e:
        logger.error(f"❌ خطأ في تمديد صلاحية المعاملة: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ أثناء تمديد صلاحية المعاملة", show_alert=True)

async def complete_payment(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """إكمال عملية الدفع لمعاملة معلقة"""
    try:
        # الحصول على معرف المعاملة من بيانات الاستدعاء
        callback_data = update.callback_query.data
        transaction_id = callback_data.replace("complete_payment_", "")

        # الحصول على معرف المستخدم
        user_id = str(update.effective_user.id)

        # الحصول على المعاملة من قاعدة البيانات
        transaction_ref = db.collection('transactions').document(transaction_id)
        transaction_doc = transaction_ref.get()

        if not transaction_doc.exists:
            await update.callback_query.answer("❌ المعاملة غير موجودة", show_alert=True)
            return

        transaction_data = transaction_doc.to_dict()

        # التحقق من أن المعاملة تخص المستخدم الحالي
        if transaction_data.get('user_id') != user_id:
            await update.callback_query.answer("❌ ليس لديك صلاحية لإكمال هذه المعاملة", show_alert=True)
            return

        # التحقق من حالة المعاملة
        if transaction_data.get('status') != 'pending':
            await update.callback_query.answer("❌ لا يمكن إكمال المعاملة لأنها ليست معلقة", show_alert=True)
            return

        # الحصول على رابط الدفع من المعاملة
        payment_url = transaction_data.get('payment_url')

        if not payment_url:
            # إنشاء رابط دفع جديد
            from services.handle_paypal_payment import create_paypal_payment_link

            # إرسال رسالة انتظار
            wait_message = await update.callback_query.message.edit_text(
                "جاري إنشاء رابط دفع جديد... يرجى الانتظار."
            )

            # إنشاء رابط دفع جديد
            payment_url, order_id = await create_paypal_payment_link(user_id)

            if not payment_url:
                await wait_message.edit_text(
                    "❌ فشل في إنشاء رابط دفع جديد. يرجى المحاولة مرة أخرى لاحقًا."
                )
                return

            # تحديث المعاملة برابط الدفع الجديد
            transaction_ref.update({
                'payment_url': payment_url,
                'order_id': order_id,
                'updated_at': datetime.now().isoformat()
            })

        # إنشاء أزرار التحكم
        keyboard = [
            [
                InlineKeyboardButton("💳 الدفع عبر PayPal", url=payment_url)
            ],
            [
                InlineKeyboardButton("✅ تأكيد الدفع", callback_data=f"verify_payment_{transaction_id}")
            ]
        ]

        # إرسال رابط الدفع للمستخدم
        await update.callback_query.message.edit_text(
            text="يرجى النقر على الزر أدناه لإكمال عملية الدفع عبر PayPal.\n\n"
                 "بعد إكمال عملية الدفع، انقر على زر 'تأكيد الدفع' للتحقق من حالة الدفع وتفعيل اشتراكك.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        logger.info(f"💳 تم إرسال رابط الدفع للمستخدم {user_id} للمعاملة {transaction_id}")

    except Exception as e:
        logger.error(f"❌ خطأ في إكمال عملية الدفع: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ أثناء إكمال عملية الدفع", show_alert=True)

async def verify_payment(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """التحقق من حالة الدفع لمعاملة معلقة"""
    try:
        # الحصول على معرف المعاملة من بيانات الاستدعاء
        callback_data = update.callback_query.data
        transaction_id = callback_data.replace("verify_payment_", "")

        # الحصول على معرف المستخدم
        user_id = str(update.effective_user.id)

        # إرسال رسالة انتظار
        await update.callback_query.answer("جاري التحقق من حالة الدفع... يرجى الانتظار.")

        # تحديث الرسالة
        wait_message = await update.callback_query.message.edit_text(
            "⏳ جاري التحقق من حالة الدفع... يرجى الانتظار."
        )

        # استدعاء دالة التحقق من الدفع
        from integrations.paypal_payment import verify_paypal_transaction

        # التحقق من حالة الدفع
        is_verified = await verify_paypal_transaction(user_id, 5.0, transaction_id)

        if is_verified:
            # تم التحقق من الدفع بنجاح
            success_keyboard = [[
                InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back_to_main")
            ]]

            await wait_message.edit_text(
                "✅ تم التحقق من الدفع بنجاح وتفعيل اشتراكك!\n\n"
                "يمكنك الآن الاستمتاع بجميع ميزات الاشتراك المدفوع.",
                reply_markup=InlineKeyboardMarkup(success_keyboard)
            )

            logger.info(f"✅ تم التحقق من الدفع بنجاح للمستخدم {user_id} للمعاملة {transaction_id}")
        else:
            # فشل في التحقق من الدفع
            # الحصول على المعاملة من قاعدة البيانات
            transaction_ref = db.collection('transactions').document(transaction_id)
            transaction_doc = transaction_ref.get()

            if not transaction_doc.exists:
                await wait_message.edit_text(
                    "❌ المعاملة غير موجودة. يرجى المحاولة مرة أخرى لاحقًا."
                )
                return

            transaction_data = transaction_doc.to_dict()
            payment_url = transaction_data.get('payment_url')

            # إنشاء أزرار التحكم
            keyboard = []

            # إضافة زر الدفع فقط إذا كان رابط الدفع متوفر
            if payment_url:
                keyboard.append([
                    InlineKeyboardButton("💳 الدفع عبر PayPal", url=payment_url)
                ])

            # إضافة زر التحقق مرة أخرى
            keyboard.append([
                InlineKeyboardButton("🔄 التحقق مرة أخرى", callback_data=f"verify_payment_{transaction_id}")
            ])

            # إضافة زر العودة للقائمة الرئيسية
            keyboard.append([
                InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back_to_main")
            ])

            await wait_message.edit_text(
                "❌ لم يتم التحقق من الدفع بعد. يرجى التأكد من إكمال عملية الدفع عبر PayPal ثم المحاولة مرة أخرى.\n\n"
                "إذا كنت قد أكملت عملية الدفع بالفعل، يرجى الانتظار بضع دقائق ثم المحاولة مرة أخرى.",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

            logger.warning(f"❌ فشل في التحقق من الدفع للمستخدم {user_id} للمعاملة {transaction_id}")

    except Exception as e:
        logger.error(f"❌ خطأ في التحقق من حالة الدفع: {str(e)}")
        await update.callback_query.message.edit_text(
            f"❌ حدث خطأ أثناء التحقق من حالة الدفع: {str(e)}"
        )

# إضافة الترجمات
translations = {
    'ar': {
        'welcome': """🌟 مرحباً بك في بوت التحليل الفني! 🌟

بوت متخصص في تحليل أسواق العملات الرقمية باستخدام المؤشرات الفنية المتقدمة. يساعدك في اتخاذ قرارات مبنية على التحليل الفني مع إمكانية إعداد تنبيهات سعرية. 📈📊✨""",
        'features': "المميزات المتوفرة:",
        'analyze': "📊 تحليل عملة رقمية",

        'set_alert': "⏰ إعداد تنبيه",
        'active_alerts': "🔔 التنبيهات النشطة",

        'manage_currencies': "💱 إدارة العملات",
        'change_language': "🌐 تغيير اللغة",
        'help': "💡 المساعدة",
        'choose_menu': "🎯 اختر من القائمة أدناه:",
        'back': "🔙 رجوع",
        'back_to_main': "🔙 القائمة الرئيسية",
        'analyzing_market': "جاري تحليل السوق...",
        'enter_symbol': "الرجاء إدخال رمز العملة (مثال: BTC/USDT أو ETH/USDT)",
        'refresh_analysis': "🔄 تحديث التحليل",
        'analyze_another': "📊 تحليل عملة أخرى",
        'other_currency': "✏️ عملة أخرى",
        'quick_alert': "🔔 إعداد تنبيه سعري",

        'add_currency': "➕ إضافة عملة",
        'remove_currency': "➖ حذف عملة",
        'currency_added': "✅ تم إضافة العملة {currency} بنجاح",
        'currency_removed': "✅ تم حذف العملة {currency} بنجاح",
        'currency_exists': "العملة موجودة بالفعل",
        'error_saving_currency': "حدث خطأ أثناء حفظ العملة",
        'no_currencies': "لا توجد عملات مخصصة لحذفها",
        'enter_currency_code': "الرجاء إدخال رمز العملة (مثال: EUR, GBP, JPY):",
        'enter_alert_price': "الرجاء إدخال السعر المستهدف لـ {symbol}:",
        'alert_condition_above': "أعلى من",
        'alert_condition_below': "أقل من",
        'alert_added': "✅ تم إضافة التنبيه بنجاح!\n\n💱 {symbol}\n⚠️ {condition} {price}\n\nسيتم إخطارك عند الوصول للسعر المحدد",
        'invalid_price': "❌ الرجاء إدخال رقم صحيح",
        'no_alerts': "لا توجد تنبيهات نشطة حالياً",
        'active_alerts_title': "التنبيهات النشطة",
        'add_new_alert': "⏰ إضافة تنبيه جديد",
        'clear_alerts': "❌ حذف جميع التنبيهات",
        'alerts_cleared': "✅ تم حذف جميع التنبيهات",


        'error_occurred': "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى",
        'symbol_not_found': "❌ عذراً، لم يتم العثور على بيانات لـ {symbol}\nتأكد من استخدام الصيغة الصحيحة (مثال: BTCUSDT)",
        'symbol_not_found_en': "❌ Sorry, couldn't find data for {symbol}\nMake sure to use correct format (example: BTCUSDT)",

        'error_analyzing': "❌ عذراً، حدث خطأ أثناء تحليل {symbol}\nالرجاء المحاولة مرة أخرى لاحقاً أو تجربة عملة أخرى",
        'error_analyzing_en': "❌ Sorry, an error occurred while analyzing {symbol}\nPlease try again later or try another currency",

        'timeout_error': "⏳ عذراً، انتهت مهلة جلب البيانات لـ {symbol}\nالرجاء المحاولة مرة أخرى",
        'timeout_error_en': "⏳ Sorry, data fetch timeout for {symbol}\nPlease try again",

        'network_error': "📡 عذراً، حدث خطأ في الاتصال\nالرجاء التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى",
        'network_error_en': "📡 Sorry, a network error occurred\nPlease check your internet connection and try again",

        'invalid_symbol': "❌ رمز العملة غير صالح\nالرجاء استخدام الصيغة الصحيحة (مثال: BTCUSDT)",
        'invalid_symbol_en': "❌ Invalid currency symbol\nPlease use the correct format (example: BTCUSDT)",

        'insufficient_data': "📊 عذراً، لا تتوفر بيانات كافية لـ {symbol}\nالرجاء المحاولة مرة أخرى لاحقاً",
        'insufficient_data_en': "📊 Sorry, insufficient data for {symbol}\nPlease try again later",

        'analyzing_market': "⏳ جاري تحليل السوق...",
        'analyzing_market_en': "⏳ Analyzing market...",
        'price_alert_notification': "🔔 تنبيه!\n\n💱 {symbol}\n💰 السعر الحالي: {current_price}\n⚠️ {condition} {price}",
        'technical_indicators': "🔍 المؤشرات الفنية:",
        'analysis_results': "📝 التحليل:",
        'local_currencies': "💱 أسعار العملات المحلية:",
        'settings_saved': "✅ تم حفظ الإعدادات",
        'help_text': """🔍 دليل استخدام البوت:

1️⃣ النسخة المجانية:
• 3 تحليلات يومياً للعملات الرقمية
• مؤشر RSI و EMA فقط
• تحديث كل ساعة
• تنبيه واحد نشط

2️⃣ مميزات النسخة المدفوعة (5 USD أسبوعياً):
• تحليل غير محدود للعملات الرقمية
• جميع المؤشرات المتقدمة:
  - مؤشر القوة النسبية (RSI)
  - الماكد (MACD)
  - مؤشر البولنجر (BB)
  - المتوسط المتحرك الأسي (EMA)
  - مؤشر القوة النسبية الاستوكاستك
  - مؤشر الاتجاه (ADX)
• تحديث فوري للتحليلات
• تنبيهات غير محدودة
• إدارة محفظة متقدمة

3️⃣ طريقة الدفع:
• PayPal
• المبلغ: 5 USD

4️⃣ خطوات الاشتراك:
1. اضغط على "✨ ترقية الحساب"
2. اضغط على زر الدفع عبر PayPal
3. أكمل عملية الدفع
4. اضغط على "تحقق من الدفع"

⚠️ ملاحظات مهمة:
• انتظر اكتمال المعاملة قبل التحقق
• الاشتراك غير قابل للاسترداد""",

'help_text_en': """🔍 Bot Usage Guide:

1️⃣ Free Version:
• 3 analyses per day for cryptocurrencies
• RSI and EMA indicators only
• Hourly updates
• One active alert

2️⃣ Premium Features (5 USD/week):
• Unlimited cryptocurrency analysis
• All advanced indicators:
  - Relative Strength Index (RSI)
  - Moving Average Convergence Divergence (MACD)
  - Bollinger Bands (BB)
  - Exponential Moving Average (EMA)
  - Stochastic RSI
  - Average Directional Index (ADX)
• Instant updates
• Unlimited alerts
• Advanced portfolio management

3️⃣ Payment Method:
• PayPal
• Amount: 5 USD

4️⃣ Subscription Steps:
1. Click "✨ Upgrade Account"
2. Click Pay with PayPal button
3. Complete payment
4. Click "Verify Payment"

⚠️ Important Notes:
• Wait for transaction completion
• Subscription is non-refundable""",
        'current_price': "السعر الحالي",
        'price_change': "التغير",
        'rsi_indicator': "مؤشر القوة النسبية (RSI)",
        'ema_indicator': "المتوسط المتحرك الأسي (EMA20/EMA50)",
        'macd_indicator': "مؤشر الماكد (MACD)",
        'bb_indicator': "نطاقات بولينجر (BB)",
        'stoch_indicator': "مؤشر القوة النسبية الستوكاستك",
        'adx_indicator': "مؤشر اتجاه الحركة (ADX)",
        'rsi_signal_oversold': "مؤشر القوة النسبية (RSI) يشير إلى منطقة ذروة البيع 🟢",
        'rsi_signal_overbought': "مؤشر القوة النسبية (RSI) يشير إلى منطقة ذروة الشراء 🔴",
        'ema_signal_bullish': "المتوسط المتحرك الأسي 20 أعلى من المتوسط المتحرك الأسي 50 - اتجاه صعودي 🟢",
        'ema_signal_bearish': "المتوسط المتحرك الأسي 20 أقل من المتوسط المتحرك الأسي 50 - اتجاه هبوطي 🔴",
        'macd_signal_buy': "مؤشر الماكد (MACD) فوق خط الإشارة - إشارة شراء محتملة 🟢",
        'macd_signal_sell': "مؤشر الماكد (MACD) تحت خط الإشارة - إشارة بيع محتملة 🔴",
        'bb_signal_oversold': "السعر تحت نطاق بولينجر السفلي (BB) - احتمال ارتداد 🟢",
        'bb_signal_overbought': "السعر فوق نطاق بولينجر العلوي (BB) - احتمال تصحيح 🔴",
        'stoch_signal_oversold': "مؤشر القوة النسبية الستوكاستك (Stoch RSI) في منطقة ذروة البيع 🟢",
        'stoch_signal_overbought': "مؤشر القوة النسبية الستوكاستك (Stoch RSI) في منطقة ذروة الشراء 🔴",
        'adx_signal_strong_bullish': "مؤشر اتجاه الحركة (ADX) يشير إلى اتجاه صعودي قوي 🟢",
        'adx_signal_strong_bearish': "مؤشر اتجاه الحركة (ADX) يشير إلى اتجاه هبوطي قوي 🔴",
        'recommendation_buy': "💚 التوصية: شراء",
        'recommendation_sell': "❤️ التوصية: بيع",
        'recommendation_hold': "💛 التوصية: انتظار",
        'choose_language': "اختر لغة البوت:",
        'select_currency_remove': "🗑️ اختر العملة التي تريد إزالتها:",
        'currency_not_found': "لم يتم العثور على العملة المحددة",
        'minutes': 'دقيقة',

        'custom_alert': '⚙️ تنبيه مخصص',
        'select_alert_price': """💱 {symbol}
💰 السعر الحالي: {current_price:.4f}

اختر نسبة التنبيه أو أدخل سعراً مخصصاً:""",
        'alert_price_hint': """📝 يمكنك تحديد السعر المستهدف بعدة طرق:

• رقم مباشر: سيتم اعتباره السعر المستهدف
مثال: 1.5

• نسبة مئوية: أضف % في النهاية
مثال:
+5% (للارتفاع)
-5% (للانخفاض)

ملاحظة: سيتم إرسال التنبيه عند الوصول للسعر المحدد ⏱️""",
        'legal_disclaimer': """
⚠️ *إخلاء المسؤولية القانوني*

1. هذا البوت يقدم تحليلات فنية فقط وليس نصائح استثمارية
2. أنت المسؤول الوحيد عن قراراتك الاستثمارية
3. لا نتحمل مسؤولية أي خسائر مالية
4. استخدام البوت يعني موافقتك على الشروط
5. عدم اتباع خطوات الاشتراك بدقة قد يؤدي لفقدان اشتراكك
6. لا يوجد دعم فني - اتبع التعليمات بدقة
7. المؤشرات الفنية قد لا تكون دقيقة 100% وتعتمد على بيانات تاريخية
8. الأسواق المالية متقلبة وتنطوي على مخاطر عالية
9. لا نضمن أي أرباح أو عوائد مالية
10. البوت قد يتوقف أو يتعطل في أي وقت دون إشعار مسبق

🔒 *شروط الاستخدام*:
• البوت للتحليل الفني فقط
• لا نضمن دقة التحليلات
• لا نقدم استشارات مالية
• الاشتراك غير قابل للاسترداد
• يحظر استخدام البوت لأغراض غير مشروعة
• يحظر مشاركة حساب الاشتراك مع الآخرين
• نحتفظ بحق إيقاف أي حساب يخالف الشروط
• قد نقوم بتحديث الشروط في أي وقت دون إشعار
• البيانات المقدمة قد تكون متأخرة عن السوق الفعلي
• لا نتحمل مسؤولية أي أعطال فنية أو انقطاع في الخدمة

⚠️ *تحذيرات إضافية*:
• تداول العملات الرقمية ينطوي على مخاطر عالية
• لا تستثمر أكثر مما يمكنك تحمل خسارته
• قم بإجراء بحثك الخاص قبل اتخاذ أي قرار
• كن حذراً من عمليات الاحتيال والمشاريع الوهمية
• تأكد من فهم آلية عمل المؤشرات الفنية قبل استخدامها
""",
        'subscription_info': """💎 نظام الاشتراك

معرف المستخدم: {chat_id}
الحالة: {status}
تاريخ الانتهاء: {expiry_date}

⚠️ تنبيهات مهمة:
• لا يوجد دعم فني - اتبع التعليمات بدقة
• أنت المسؤول الوحيد عن عملية الدفع
• الاشتراك غير قابل للاسترداد
• الدفع يعني موافقتك على جميع الشروط
• عدم اتباع الخطوات قد يؤدي لفقدان الاشتراك

خطوات الاشتراك:
1️⃣ اضغط على زر "الدفع عبر PayPal" أدناه

2️⃣ أكمل عملية الدفع (5 USD)

3️⃣ ⚠️ تعليمات مهمة:
• انتظر اكتمال المعاملة (2-5 دقائق)
• احتفظ برقم المعاملة للمراجعة

4️⃣ اضغط 'تحقق من الدفع' بعد اكتمال الدفع

🔴 تحذيرات إضافية:
• تأكد من اكتمال المعاملة قبل التحقق
• الاشتراك يبدأ من تاريخ الدفع
• لا تقم بالتحويل أكثر من مرة
• انتظر التحقق من الدفع""",
        'subscription_info_en': """💎 Subscription System

User ID: {chat_id}
Status: {status}
Expiry Date: {expiry_date}

⚠️ Important Notes:
• No technical support - follow instructions carefully
• You are solely responsible for the payment
• Subscription is non-refundable
• Payment implies acceptance of all terms
• Not following steps may result in subscription loss

Subscription Steps:
1️⃣ Click the "Pay with PayPal" button below

2️⃣ Complete the payment (5 USD)

3️⃣ ⚠️ Important Instructions:
• Wait for transaction completion (2-5 minutes)
• Keep transaction ID for reference

4️⃣ Click 'Verify Payment' after payment is complete

🔴 Additional Warnings:
• Ensure transaction is complete before verification
• Subscription starts from payment date
• Do not make payment more than once
• Wait for payment verification""",
        'subscribed': 'مشترك',
        'not_subscribed': 'غير مشترك',
        'payment_verified': '✅ تم التحقق من الدفع وتفعيل اشتراكك!',
        'payment_not_found': '❌ لم يتم العثور على الدفع. تأكد من:\n1. تحويل المبلغ الصحيح (5 USD)\n\n3. استخدام PayPal\n4. اكتمال المعاملة (2-5 دقائق)',
        'payment_not_found_en': '❌ Payment not found. Make sure:\n1. Correct amount transferred (5 USD)\n3. Using PayPal\n4. Transaction is completed (2-5 minutes)',
        'check_again': 'تحقق مرة أخرى',
        'verify_payment_btn': 'تحقق من الدفع',

        # إضافة رسائل timeout
        'timeout_error': '⚠️ عذراً، انتهت مهلة جلب البيانات لـ {symbol}. يرجى المحاولة مرة أخرى.',
        'timeout_error_en': '⚠️ Sorry, data fetch timeout for {symbol}. Please try again.',

        'indicator_toggled': {
            'ar': 'تم {} مؤشر {}',
            'en': '{} indicator {}'
        },
        'added': {
            'ar': 'إضافة',
            'en': 'Added'
        },
        'removed': {
            'ar': 'إزالة',
            'en': 'Removed'
        },
        'select_currency_remove': '🗑️ اختر العملة التي تريد إزالتها:',
        'select_currency_add': '➕ اختر العملة التي تريد إضافتها:',
        'currency_management': '💱 إدارة العملات المخصصة',
        'no_custom_currencies': 'لا توجد عملات مخصصة حالياً',
        'currency_added_success': 'تم إضافة عملة {} بنجاح ✅',
        'currency_removed_success': 'تم إزالة عملة {} بنجاح ✅',
        'active_indicators': 'المؤشرات المفعلة',
        'available_indicators': 'المؤشرات المتاحة',
        'set_alert_help': (
            "🔔 *إعداد تنبيه سعري جديد*\n\n"
            "📝 الرجاء إدخال رمز العملة بالتنسيق التالي:\n"
            "`BTC/USDT` أو `ETH/USDT`\n\n"
            "⚠️ *ملاحظات مهمة*:\n"
            "• استخدم `/` بين العملتين\n"
            "• الرموز يجب أن تكون بالإنجليزية\n"
            "• تأكد من صحة الرمز قبل الإرسال"
        ),
'upgrade_message': """✨ *ترقية الحساب*

💎 احصل على جميع المميزات المتقدمة مقابل 5 USD أسبوعياً:

📊 *المميزات*:
• تحليلات غير محدودة للعملات الرقمية
• جميع المؤشرات الفنية
• تنبيهات سعرية غير محدودة
• تحديثات فورية
• إدارة محفظة متقدمة

💳 *طريقة الدفع*:
• PayPal
• المبلغ: 5 USD

⚠️ *ملاحظات مهمة*:
• انتظر اكتمال المعاملة (2-5 دقائق)
• الاشتراك غير قابل للاسترداد""",

'upgrade_message_en': """✨ *Account Upgrade*

💎 Get all advanced features for 5 USD weekly:

📊 *Features*:
• Unlimited cryptocurrency analyses
• All technical indicators
• Unlimited price alerts
• Instant updates
• Advanced portfolio management

💳 *Payment Method*:
• PayPal
• Amount: 5 USD

⚠️ *Important Notes*:
• Wait for transaction completion (2-5 mins)
• Subscription is non-refundable""",
        'pay_button': "💳 الدفع",
        'back_button': "🔙 رجوع",
        'upgrade_title': 'ترقية الحساب ✨',
        'upgrade_description': 'احصل على جميع المميزات المتقدمة مقابل 5 USD أسبوعياً',
        'features_title': 'المميزات',
        'feature1': '📊 تحليلات غير محدودة',
        'feature2': '🔍 جميع المؤشرات الفنية',
        'feature3': '🔔 تنبيهات سعرية غير محدودة',

        'feature5': '⚙️ إدارة محفظة متقدمة',
        'payment_title': 'معلومات الدفع',
        'currency_label': 'العملة',
        'network_label': 'الشبكة',
        'address_label': 'عنوان المحفظة',
        'notes_title': 'ملاحظات مهمة',
        'payment_note1': 'استخدم PayPal فقط للدفع',

        'payment_note3': 'انتظر اكتمال المعاملة (2-5 دقائق)',
        'verify_payment_btn': '✅ تحقق من الدفع',
        'back_button': '🔙 رجوع',
        'already_subscribed': '✅ أنت مشترك بالفعل في النسخة المميزة',
        'payment_verification': """💳 *التحقق من الدفع*

معرف المعاملة: `{transaction_id}`

⚠️ *تعليمات مهمة*:
1. قم بدفع 5 USD بالضبط
2. أكمل عملية الدفع
3. انتظر اكتمال المعاملة (2-5 دقائق)
4. اضغط على زر التحقق من الدفع

🔴 *تحذير*: عدم اتباع التعليمات قد يؤدي لفقدان اشتراكك""",
        'payment_verification_en': """💳 *Payment Verification*

Transaction ID: `{transaction_id}`

⚠️ *Important Instructions*:
1. Transfer exactly 5 USD
2. Complete payment
3. Wait for transaction completion (2-5 mins)
4. Click verify payment button

🔴 *Warning*: Not following instructions may result in subscription loss""",
        'subscription_success': """✅ تم التحقق من الدفع بنجاح!
🌟 تم تفعيل اشتراكك لمدة أسبوع

يمكنك الآن الاستمتاع بجميع المميزات المتقدمة:
• تحليلات غير محدودة للعملات الرقمية
• جميع المؤشرات الفنية
• تنبيهات سعرية غير محدودة
• استراتيجيات تداول آلية
• تنبؤات سعرية
• تحليل متعدد الإطارات الزمنية
""",
        'subscription_failed': """❌ فشل التحقق من الدفع

الأسباب المحتملة:
• لم يتم العثور على المعاملة
• المعاملة مستخدمة من قبل
• المبلغ غير صحيح
• معرف المستخدم غير مطابق

الرجاء التأكد من اتباع التعليمات بدقة والمحاولة مرة أخرى""",
        'total_alerts': 'إجمالي التنبيهات النشطة',
        'no_alerts': 'لا توجد تنبيهات نشطة حالياً',
        'active_alerts_title': 'التنبيهات النشطة',
        'terms_button': "📜 الشروط والأحكام",
        'legal_disclaimer': """
⚠️ *إخلاء المسؤولية القانوني*

1. هذا البوت يقدم تحليلات فنية فقط وليس نصائح استثمارية
2. أنت المسؤول الوحيد عن قراراتك الاستثمارية
3. لا نتحمل مسؤولية أي خسائر مالية
4. استخدام البوت يعني موافقتك على الشروط
5. عدم اتباع خطوات الاشتراك بدقة قد يؤدي لفقدان اشتراكك
6. لا يوجد دعم فني - اتبع التعليمات بدقة
7. المؤشرات الفنية قد لا تكون دقيقة 100% وتعتمد على بيانات تاريخية
8. الأسواق المالية متقلبة وتنطوي على مخاطر عالية
9. لا نضمن أي أرباح أو عوائد مالية
10. البوت قد يتوقف أو يتعطل في أي وقت دون إشعار مسبق

🔒 *شروط الاستخدام*:
• البوت للتحليل الفني فقط
• لا نضمن دقة التحليلات
• لا نقدم استشارات مالية
• الاشتراك غير قابل للاسترداد
• يحظر استخدام البوت لأغراض غير مشروعة
• يحظر مشاركة حساب الاشتراك مع الآخرين
• نحتفظ بحق إيقاف أي حساب يخالف الشروط
• قد نقوم بتحديث الشروط في أي وقت دون إشعار
• البيانات المقدمة قد تكون متأخرة عن السوق الفعلي
• لا نتحمل مسؤولية أي أعطال فنية أو انقطاع في الخدمة

⚠️ *تحذيرات إضافية*:
• تداول العملات الرقمية ينطوي على مخاطر عالية
• لا تستثمر أكثر مما يمكنك تحمل خسارته
• قم بإجراء بحثك الخاص قبل اتخاذ أي قرار
• كن حذراً من عمليات الاحتيال والمشاريع الوهمية
• تأكد من فهم آلية عمل المؤشرات الفنية قبل استخدامها
""",
        'fib_indicator': "مؤشر فيبوناتشي (Fibonacci)",
        'ichimoku_indicator': "سحابة إيشيموكو (Ichimoku)",
        'vol_profile_indicator': "حجم التداول (Volume Profile)",
        'order_book_indicator': "سجل الأوامر (Order Book)",
        'vwap_indicator': "السعر المتوسط المرجح بالحجم (VWAP)",
    },
    'en': {
        'select_currency_add': '➕ Choose currency to add:',
        'select_currency_remove': '🗑️ Choose currency to remove:',
        'welcome': """🌟 Welcome to Technical Analysis Bot! 🌟

A specialized bot for analyzing cryptocurrency markets using advanced technical indicators. Helps you make decisions based on technical analysis with the ability to set price alerts. 📈📊✨""",
        'features': "Available Features:",
        'analyze': "📊 Analyze Cryptocurrency",

        'set_alert': "⏰ Set Alert",
        'active_alerts': "🔔 Active Alerts",

        'manage_currencies': "💱 Manage Currencies",
        'change_language': "🌐 Change Language",
        'help': "💡 Help",
        'choose_menu': "🎯 Choose from the menu below:",
        'back': "🔙 Back",
        'back_to_main': "🔙 Main Menu",
        'analyzing_market': "Analyzing market...",
        'enter_symbol': "Please enter currency pair (example: BTC/USDT or ETH/USDT)",
        'refresh_analysis': "🔄 Refresh Analysis",
        'analyze_another': "📊 Analyze Another",
        'other_currency': "✏️ Other Currency",
        'quick_alert': "🔔 Set Price Alert",

        'add_currency': "➕ Add Currency",
        'remove_currency': "➖ Remove Currency",
        'currency_added': "✅ Currency {currency} added successfully",
        'currency_removed': "✅ Currency {currency} removed successfully",
        'currency_exists': "Currency already exists",
        'error_saving_currency': "Error saving currency",
        'no_currencies': "No custom currencies to remove",
        'enter_currency_code': "Please enter currency code (example: EUR, GBP, JPY):",
        'enter_alert_price': "Please enter target price for {symbol}:",
        'alert_condition_above': "Above",
        'alert_condition_below': "Below",
        'alert_added': "✅ Alert added successfully!\n\n💱 {symbol}\n⚠️ {condition} {price}",
        'invalid_price': "❌ Please enter a valid number",
        'no_alerts': "No active alerts currently",
        'active_alerts_title': "Active Alerts",
        'add_new_alert': "⏰ Add New Alert",
        'clear_alerts': "❌ Clear All Alerts",
        'alerts_cleared': "✅ All alerts cleared",


        'error_occurred': "Sorry, an error occurred. Please try again",
        'symbol_not_found': "❌ Sorry, couldn't find data for {symbol}\nMake sure to use correct format (example: BTCUSDT)",
        'error_analyzing': "❌ Sorry, an error occurred while analyzing {symbol}\nPlease try again later or try another currency",
        'price_alert_notification': "🔔 Alert!\n\n💱 {symbol}\n💰 Current Price: {current_price}\n⚠️ {condition} {price}",
        'technical_indicators': "🔍 Technical Indicators:",
        'analysis_results': "📝 Analysis:",
        'local_currencies': "💱 Local Currency Prices:",
        'settings_saved': "✅ Settings saved",
        'help_text': """🔍 Bot Usage Guide:

1️⃣ Free Version:
• 3 analyses per day for cryptocurrencies
• RSI and EMA indicators only
• Hourly updates
• One active alert

2️⃣ Premium Features (5 USD/week):
• Unlimited cryptocurrency analysis
• All advanced indicators:
  - Relative Strength Index (RSI)
  - Moving Average Convergence Divergence (MACD)
  - Bollinger Bands (BB)
  - Exponential Moving Average (EMA)
  - Stochastic RSI
  - Average Directional Index (ADX)
• Advanced analysis features:
  - Trading strategies
  - Price predictions
  - Multi-timeframe analysis
• Instant updates
• Unlimited alerts
• Advanced portfolio management

3️⃣ Payment Method:
• PayPal
• Amount: 5 USD

4️⃣ Subscription Steps:
1. Click "✨ Upgrade Account"
2. Click PayPal payment button
3. Complete the payment
4. Click "Verify Payment" button

⚠️ Important Notes:
• Wait for transaction completion (2-5 mins)
• Subscription is non-refundable""",
        'current_price': "Current Price",
        'price_change': "Change",
        'rsi_indicator': "Relative Strength Index (RSI)",
        'ema_indicator': "Exponential Moving Average (EMA20/EMA50)",
        'macd_indicator': "Moving Average Convergence Divergence (MACD)",
        'bb_indicator': "Bollinger Bands (BB)",
        'stoch_indicator': "Stochastic RSI",
        'adx_indicator': "Average Directional Index (ADX)",
        'rsi_signal_oversold': "RSI indicates oversold region 🟢",
        'rsi_signal_overbought': "RSI indicates overbought region 🔴",
        'ema_signal_bullish': "EMA20 above EMA50 - Bullish trend 🟢",
        'ema_signal_bearish': "EMA20 below EMA50 - Bearish trend 🔴",
        'macd_signal_buy': "MACD above signal line - Potential buy signal 🟢",
        'macd_signal_sell': "MACD below signal line - Potential sell signal 🔴",
        'bb_signal_oversold': "Price below lower BB - Potential bounce 🟢",
        'bb_signal_overbought': "Price above upper BB - Potential correction 🔴",
        'stoch_signal_oversold': "Stochastic RSI in oversold region 🟢",
        'stoch_signal_overbought': "Stochastic RSI in overbought region 🔴",
        'adx_signal_strong_bullish': "ADX indicates strong bullish trend 🟢",
        'adx_signal_strong_bearish': "ADX indicates strong bearish trend 🔴",
        'recommendation_buy': "💚 Recommendation: Buy",
        'recommendation_sell': "❤️ Recommendation: Sell",
        'recommendation_hold': "💛 Recommendation: Hold",
        'choose_language': "Choose bot language:",
        'subscribed': 'Subscribed',
        'not_subscribed': 'Not Subscribed',
        'payment_verified': '✅ Payment verified and subscription activated!',
        'payment_not_found': '❌ Payment not found. Make sure:\n1. Correct amount transferred (5 USD)\n3. Using PayPal\n4. Transaction is completed (2-5 minutes)',
        'check_again': 'Check Again',
        'verify_payment_btn': 'Verify Payment',
        'indicator_toggled': {
            'ar': 'تم {} مؤشر {}',
            'en': '{} indicator {}'
        },
        'added': {
            'ar': 'إضافة',
            'en': 'Added'
        },
        'removed': {
            'ar': 'إزالة',
            'en': 'Removed'
        },
        'upgrade_message': """✨ *Account Upgrade*

💎 Get all advanced features for 5 USD weekly:

📊 *Features*:
• Unlimited analyses
• All technical indicators
• Unlimited price alerts
• Advanced analysis features:
  - Trading strategies
  - Price predictions
  - Multi-timeframe analysis
• Instant updates
• Advanced portfolio management

💳 *Payment Method*:
• PayPal
• Amount: 5 USD

⚠️ *Important Notes*:
• Wait for transaction completion (2-5 mins)
• Subscription is non-refundable""",
        'pay_button': "💳 Pay",
        'back_button': "🔙 Back",
        'upgrade_title': 'Account Upgrade ✨',
        'upgrade_description': 'Get all advanced features for 5 USD weekly',
        'features_title': 'Features',
        'feature1': '📊 Unlimited analyses',
        'feature2': '🔍 All technical indicators',
        'feature3': '🔔 Unlimited price alerts',

        'feature5': '⚙️ Advanced portfolio management',
        'payment_title': 'Payment Information',
        'currency_label': 'Currency',
        'network_label': 'Network',
        'address_label': 'Wallet Address',
        'notes_title': 'Important Notes',
        'payment_note1': 'Use PayPal only for payment',

        'payment_note3': 'Wait for transaction completion (2-5 mins)',
        'verify_payment_btn': '✅ Verify Payment',
        'back_button': '🔙 Back',
        'already_subscribed': '✅ You are already subscribed to the premium version',
        'subscription_info': """💎 Subscription System

User ID: {chat_id}
Status: {status}
Expiry Date: {expiry_date}

⚠️ Important Notes:
• No technical support - follow instructions carefully
• You are solely responsible for the payment
• Subscription is non-refundable
• Payment implies acceptance of all terms
• Not following steps may result in subscription loss

Subscription Steps:
1️⃣ Click the "Pay with PayPal" button below

2️⃣ Complete the payment (5 USD)

3️⃣ ⚠️ Important Instructions:
• Wait for transaction completion (2-5 minutes)
• Keep transaction ID for reference

4️⃣ Click 'Verify Payment' after payment is complete

🔴 Additional Warnings:
• Ensure transaction is complete before verification
• Subscription starts from payment date
• Do not make payment more than once
• Wait for payment verification""",
        'verify_payment_btn': "✅ Verify Payment",
        'back_to_main': "🔙 Main Menu",
        'not_subscribed': "Not Subscribed",
        'analyzing_market': "Analyzing market...",
        'current_price': "Current Price",
        'price_change': "Price Change",
        'technical_indicators': "🔍 Technical Indicators:",
        'analysis_results': "📝 Analysis Results:",
        'local_currencies': "💱 Local Currency Prices:",
        'rsi_indicator': "Relative Strength Index (RSI)",
        'ema_indicator': "Exponential Moving Average (EMA20/EMA50)",
        'macd_indicator': "Moving Average Convergence Divergence (MACD)",
        'bb_indicator': "Bollinger Bands (BB)",
        'stoch_indicator': "Stochastic RSI",
        'adx_indicator': "Average Directional Index (ADX)",
        'rsi_signal_oversold': "RSI indicates oversold region 🟢",
        'rsi_signal_overbought': "RSI indicates overbought region 🔴",
        'ema_signal_bullish': "EMA20 above EMA50 - Bullish trend 🟢",
        'ema_signal_bearish': "EMA20 below EMA50 - Bearish trend 🔴",
        'macd_signal_buy': "MACD above signal line - Potential buy signal 🟢",
        'macd_signal_sell': "MACD below signal line - Potential sell signal 🔴",
        'bb_signal_oversold': "Price below lower BB - Potential bounce 🟢",
        'bb_signal_overbought': "Price above upper BB - Potential correction 🔴",
        'stoch_signal_oversold': "Stochastic RSI in oversold region 🟢",
        'stoch_signal_overbought': "Stochastic RSI in overbought region 🔴",
        'adx_signal_strong_bullish': "ADX indicates strong bullish trend 🟢",
        'adx_signal_strong_bearish': "ADX indicates strong bearish trend 🔴",
        'recommendation_buy': "💚 Recommendation: Buy",
        'recommendation_sell': "❤️ Recommendation: Sell",
        'recommendation_hold': "💛 Recommendation: Hold",
        'analyze': "📊 Analysis for",
        'paypal_payment': """💳 *Pay with PayPal*

💰 المبلغ: 5 USD
🔗 رابط الدفع: `{paypal_link}`

⚠️ *تعليمات مهمة*:
1. اضغط على زر الدفع أدناه

3. أكمل عملية الدفع
4. انتظر اكتمال المعاملة (2-5 دقائق)
5. اضغط على زر "تحقق من الدفع"

🔴 *ملاحظات*:
• تأكد من إدخال معرف المستخدم بشكل صحيح
• لا تغلق الصفحة حتى اكتمال عملية الدفع
• الاشتراك غير قابل للاسترداد""",

        'paypal_payment_en': """💳 *PayPal Payment*

💰 Amount: 5 USD
🔗 Payment Link: `{paypal_link}`

⚠️ *Important Instructions*:
1. Click the payment button below
2. Enter User ID `{chat_id}` in payment notes
3. Complete the payment
4. Wait for transaction completion (2-5 mins)
5. Click "Verify Payment" button

🔴 *Notes*:
• Make sure to enter the correct User ID
• Don't close the page until payment is complete
• Subscription is non-refundable""",

        'verify_payment': """✅ *Payment Verification*

⚠️ *Verification Instructions*:
1. Ensure payment is completed
2. Make sure User ID is added in payment notes
3. Wait 2-5 minutes for completion
4. Click verify button below

🔍 Your subscription will be activated automatically after verification""",

        'payment_button': "💳 Pay with PayPal",
        'verify_button': "✅ Verify Payment",
        'back_button': "🔙 Back",
    }
}

def get_text(key: str, lang: str = 'ar') -> str:
    """الحصول على النص المترجم"""
    # التأكد من أن اللغة صالحة
    if lang not in ['ar', 'en']:
        lang = 'ar'

    # الحصول على النص من القاموس المناسب
    translations_dict = translations.get(lang, translations['ar'])
    text = translations_dict.get(key, translations_dict.get('missing_text', f"Missing text: {key}"))

    # إذا كان النص قاموساً (مثل indicator_toggled)
    if isinstance(text, dict):
        text = text.get(lang, text.get('ar', f"Missing text: {key}"))

    return text

async def set_language(update: Update, context: CallbackContext, lang: str):
    """تغيير لغة البوت"""
    if not update.callback_query:
        return

    user_id = str(update.callback_query.from_user.id)

    # حفظ إعدادات اللغة في Firestore
    settings_ref = db.collection('user_settings').document(user_id)
    settings = {'lang': lang}
    settings_ref.set(settings, merge=True)

    # تحديث الذاكرة المؤقتة
    firestore_cache.set(f'settings_{user_id}', settings, ex=subscription_system.cache_timeout, cache_type="user_data")

    # إرسال رسالة تأكيد باللغة المناسبة
    success_message = "✅ Language changed to English!" if lang == 'en' else "✅ تم تغيير اللغة إلى العربية!"
    await update.callback_query.answer(success_message)

    # تحديث القائمة الرئيسية باللغة الجديدة
    try:
        menu_text = get_main_menu_text(user_id, lang)
        keyboard = get_main_menu_keyboard(user_id, lang)

        await update.callback_query.edit_message_text(
            text=menu_text,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )
    except Exception as e:
        logger.error(f"Error updating main menu after language change: {str(e)}")
        await update.callback_query.message.reply_text(
            text=menu_text,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )

async def get_main_menu_text(user_id: str, lang: str) -> str:
    """إنشاء نص القائمة الرئيسية"""
    try:
        logger.info(f"جاري إنشاء نص القائمة الرئيسية للمستخدم {user_id} باللغة {lang}")

        # التحقق من حالة الاشتراك
        logger.info(f"جاري التحقق من حالة اشتراك المستخدم {user_id}...")
        subscription_system = SubscriptionSystem()
        try:
            subscription_details = subscription_system.get_subscription_status(user_id, full_details=True)
            is_premium = subscription_details['is_active']
            is_free_day = subscription_details.get('is_free_day', False)
            logger.info(f"حالة اشتراك المستخدم {user_id}: {is_premium}, يوم مجاني: {is_free_day}")
        except Exception as e:
            logger.error(f"خطأ في التحقق من حالة اشتراك المستخدم {user_id}: {str(e)}")
            is_premium = False
            is_free_day = False

        # التحقق من وجود مفاتيح API
        has_binance_api = False
        has_gemini_api = False

        # التأكد من تهيئة مدير API
        global api_manager
        logger.info(f"التحقق من تهيئة مدير API للمستخدم {user_id}...")
        if api_manager is not None:
            logger.info(f"مدير API متاح للمستخدم {user_id}")
            try:
                logger.info(f"جاري التحقق من مفاتيح Binance API للمستخدم {user_id}...")
                has_binance_api = await api_manager.has_api_keys(user_id, 'binance')
                logger.info(f"جاري التحقق من مفاتيح Gemini API للمستخدم {user_id}...")
                has_gemini_api = await api_manager.has_api_keys(user_id, 'gemini')
                logger.info(f"حالة مفاتيح API للمستخدم {user_id}: Binance={has_binance_api}, Gemini={has_gemini_api}")
            except Exception as e:
                logger.error(f"خطأ في التحقق من مفاتيح API: {str(e)}")
                logger.error(f"تفاصيل الخطأ: {type(e).__name__}")
        else:
            logger.error(f"مدير API غير متاح للمستخدم {user_id}")

        # نص الترحيب
        menu_text = get_text('welcome', lang) + "\n\n"

        if is_premium:
            logger.info(f"المستخدم {user_id} مشترك، جاري جلب تفاصيل الاشتراك...")
            try:
                subscription_info = subscription_system.get_subscription_details(user_id)
                logger.info(f"تم جلب تفاصيل الاشتراك للمستخدم {user_id}: {subscription_info}")

                # التحقق مما إذا كان اليوم المجاني مفعل
                if is_free_day:
                    menu_text += "🎁 " + (
                        f"Free Day Active Today!\nEnjoy premium features for free today.\n\n"
                        if lang == 'en' else
                        f"اليوم المجاني مفعل اليوم!\nاستمتع بالميزات المدفوعة مجاناً اليوم.\n\n"
                    )
                else:
                    menu_text += "⭐️ " + (
                        f"Subscription Status: Premium\n📅 Expiry Date: {subscription_info['expiry']}\n\n"
                        if lang == 'en' else
                        f"حالة الاشتراك: مشترك\n📅 تاريخ الانتهاء: {subscription_info['expiry']}\n\n"
                    )
            except Exception as e:
                logger.error(f"خطأ في جلب تفاصيل الاشتراك للمستخدم {user_id}: {str(e)}")
                menu_text += "⭐️ " + (
                    f"Subscription Status: Premium\n\n"
                    if lang == 'en' else
                    f"حالة الاشتراك: مشترك\n\n"
                )
        else:
            # جلب بيانات الاستخدام المجاني المحدثة
            logger.info(f"المستخدم {user_id} غير مشترك، جاري جلب بيانات الاستخدام المجاني...")

            # التحقق مما إذا كان اليوم المجاني مفعل
            if is_free_day:
                # الحصول على معلومات اليوم المجاني
                try:
                    # Verificar si el día gratuito está activo
                    is_free_day_active = free_day_system.is_free_day_active(user_id)
                    menu_text += "🎁 " + (
                        f"Free Day Active Today!\nEnjoy premium features for free today.\n\n"
                        if lang == 'en' else
                        f"اليوم المجاني مفعل اليوم!\nاستمتع بالميزات المدفوعة مجاناً اليوم.\n\n"
                    )
                except Exception as e:
                    logger.error(f"خطأ في جلب معلومات اليوم المجاني للمستخدم {user_id}: {str(e)}")

            try:
                free_usage = subscription_system.get_free_usage(user_id)
                logger.info(f"تم جلب بيانات الاستخدام المجاني للمستخدم {user_id}: {free_usage}")

                # إضافة معلومات اليوم المجاني القادم إذا لم يكن مفعلاً اليوم
                if not is_free_day:
                    try:
                        # نستخدم get_user_free_day_status هنا لأننا نحتاج إلى معلومات إضافية مثل اليوم القادم واسم اليوم
                        free_day_status = free_day_system.get_user_free_day_status(user_id)
                        next_free_day = free_day_status['next_free_day']
                        day_name = free_day_status['day_name']

                        # إضافة معلومات اليوم المجاني القادم
                        if lang == 'ar':
                            menu_text += f"🎁 اليوم المجاني القادم: {day_name} ({next_free_day.strftime('%Y-%m-%d')})\n"
                        else:
                            menu_text += f"🎁 Next Free Day: {day_name} ({next_free_day.strftime('%Y-%m-%d')})\n"
                    except Exception as e:
                        logger.error(f"خطأ في جلب معلومات اليوم المجاني القادم للمستخدم {user_id}: {str(e)}")

                menu_text += "📊 " + (
                    f"Remaining Free Usage:\n"
                    f"🔍 Analyses: {free_usage['analyses']}/3\n"
                    f"🔔 Alerts: {free_usage['alerts']}/1\n\n"
                    if lang == 'en' else
                    f"الاستخدامات المجانية المتبقية:\n"
                    f"🔍 تحليلات: {free_usage['analyses']}/3\n"
                    f"🔔 تنبيهات: {free_usage['alerts']}/1\n\n"
                )
            except Exception as e:
                logger.error(f"خطأ في جلب بيانات الاستخدام المجاني للمستخدم {user_id}: {str(e)}")
                menu_text += "📊 " + (
                    f"Remaining Free Usage:\n"
                    f"🔍 Analyses: 3/3\n"
                    f"🔔 Alerts: 1/1\n\n"
                    if lang == 'en' else
                    f"الاستخدامات المجانية المتبقية:\n"
                    f"🔍 تحليلات: 3/3\n"
                    f"🔔 تنبيهات: 1/1\n\n"
                )

        # Add free services section
        menu_text += "🔹 " + get_text('features', lang) + "\n"
        menu_text += "• " + get_text('analyze', lang) + "\n"
        menu_text += "• " + get_text('active_alerts', lang) + "\n"
        menu_text += "• " + get_text('help', lang) + "\n"
        menu_text += "• " + get_text('change_language', lang) + "\n"

        # إضافة معلومات عن حالة API
        api_status = "🔑 API: "

        # الحصول على معلومات API
        api_info = {}
        if api_manager:
            try:
                api_info = await api_manager.get_api_info(user_id)
            except Exception as e:
                logger.error(f"خطأ في الحصول على معلومات API: {str(e)}")

        # قائمة المنصات المدعومة
        platforms = {
            'binance': 'Binance',
            'gemini': 'Gemini',
            'kucoin': 'KuCoin',
            'coinbase': 'Coinbase',
            'bybit': 'Bybit',
            'okx': 'OKX',
            'kraken': 'Kraken'
        }

        # إضافة حالة كل منصة
        platform_statuses = []
        for platform_id, platform_name in platforms.items():
            # تخطي Gemini للمستخدمين غير المشتركين
            if platform_id == 'gemini' and not is_premium:
                continue

            has_platform = api_info.get(f'has_{platform_id}', False)
            platform_statuses.append(f"{platform_name} {'✅' if has_platform else '❌'}")

        api_status += " ".join(platform_statuses)
        menu_text += "• " + api_status + "\n"

        # إضافة تنبيه حول استخدام API العام
        if not has_binance_api:
            menu_text += "\n⚠️ " + (
                "Note: Currently using Binance public API which has limited data and request limits. For better performance, add your own API keys."
                if lang == 'en' else
                "ملاحظة: يتم حالياً استخدام واجهة Binance العامة التي لها بيانات وحدود طلبات محدودة. للحصول على أداء أفضل، أضف مفاتيح API الخاصة بك."
            ) + "\n"

        if is_premium:
            menu_text += "\n⭐️ " + (
                "Premium Services:" if lang == 'en' else "الخدمات المميزة:"
            ) + "\n"
            menu_text += "• " + (
                "🔔 Unlimited price alerts" if lang == 'en' else "🔔 تنبيهات سعرية غير محدودة"
            ) + "\n"
            menu_text += "• " + (
                "⚙️ Advanced customization" if lang == 'en' else "⚙️ إعدادات وتخصيص متقدم"
            ) + "\n"
            menu_text += "• " + (
                "📈 Unlimited analyses" if lang == 'en' else "📈 تحليلات غير محدودة"
            ) + "\n"
            menu_text += "• " + (
                "🎯 All technical indicators" if lang == 'en' else "🎯 جميع المؤشرات الفنية"
            )
        else:
            menu_text += "\n✨ " + (
                "Get additional features (5 USD weekly):\n"
                "• 🔔 Unlimited price alerts\n"
                "• ⚙️ Advanced customization\n"
                "• 📈 Unlimited analyses\n"
                "• 🎯 All technical indicators\n"
                "• 🤖 Gemini AI analysis\n\n"
                "Click ✨ Upgrade Account to subscribe"
                if lang == 'en' else
                "للحصول على مميزات إضافية (5 USD أسبوعياً):\n"
                "• 🔔 تنبيهات سعرية غير محدودة\n"
                "• ⚙️ إعدادات وتخصيص متقدم\n"
                "• 📈 تحليلات غير محدودة\n"
                "• 🎯 جميع المؤشرات الفنية\n"
                "• 🤖 تحليل بالذكاء الاصطناعي Gemini\n\n"
                "اضغط على ✨ ترقية الحساب للاشتراك"
            )

        # إضافة معلومات عن إعداد API
        if not has_binance_api:
            menu_text += "\n🔑 " + (
                "Setup your Binance API for better market data and higher request limits.\nClick on 'API Setup' button below."
                if lang == 'en' else
                "قم بإعداد Binance API للحصول على بيانات سوق أفضل وحدود طلبات أعلى.\nاضغط على زر 'إعداد API' أدناه."
            )

        if is_premium and not has_gemini_api:
            menu_text += "\n🤖 " + (
                "Setup your Gemini API for AI analysis (required for advanced features).\nClick on 'API Setup' button below."
                if lang == 'en' else
                "قم بإعداد Gemini API للتحليل بالذكاء الاصطناعي (مطلوب للميزات المتقدمة).\nاضغط على زر 'إعداد API' أدناه."
            )



        logger.info(f"تم إنشاء نص القائمة الرئيسية للمستخدم {user_id} بنجاح")
        return menu_text

    except Exception as e:
        logger.error(f"خطأ في إنشاء نص القائمة الرئيسية: {str(e)}")
        # إرجاع نص بسيط في حالة الخطأ
        return "👋 مرحباً بك في بوت التحليل الفني!\n\nيرجى المحاولة مرة أخرى لاحقاً." if lang == 'ar' else "👋 Welcome to Technical Analysis Bot!\n\nPlease try again later."



def get_main_menu_keyboard(user_id: str, lang: str) -> InlineKeyboardMarkup:
    """إنشاء لوحة مفاتيح القائمة الرئيسية"""
    subscription_system = SubscriptionSystem()
    subscription_details = subscription_system.get_subscription_status(user_id, full_details=True)
    is_premium = subscription_details['is_active']
    is_free_day = subscription_details.get('is_free_day', False)

    # التحقق من وجود مفاتيح API للمستخدم
    # نستخدم طريقة متزامنة للتحقق من وجود مفاتيح API
    has_gemini_api = False
    if api_manager:
        try:
            # التحقق من مفتاح Gemini API
            gemini_key, _ = api_manager.get_api_keys_sync(user_id, 'gemini')
            has_gemini_api = bool(gemini_key)
        except Exception as e:
            logger.error(f"خطأ في التحقق من مفاتيح API: {str(e)}")
            has_gemini_api = False

    keyboard = [
        [InlineKeyboardButton(get_text('analyze', lang), callback_data='analyze')],
        [InlineKeyboardButton(get_text('active_alerts', lang), callback_data='active_alerts')],
        [InlineKeyboardButton("🧠 تعلم مع الذكاء الاصطناعي" if lang == 'ar' else "🧠 Learn with AI", callback_data='learn_trading_ai')], # زر تعلم التداول بالذكاء الاصطناعي
        [InlineKeyboardButton("🔑 إعداد API" if lang == 'ar' else "🔑 API Setup", callback_data='setup_api_keys')],
    ]

    # إضافة زر الدردشة مع الذكاء الاصطناعي للمشتركين الذين لديهم مفتاح Gemini API
    if is_premium and has_gemini_api:
        keyboard.insert(2, [InlineKeyboardButton("🤖 الدردشة مع AI" if lang == 'ar' else "🤖 Chat with AI", callback_data='ai_chat')])

    # إضافة زر إدارة اليوم المجاني
    if not is_premium or is_free_day:
        keyboard.append([
            InlineKeyboardButton(
                "🎁 Free Day Settings" if lang == 'en' else "🎁 إعدادات اليوم المجاني",
                callback_data='free_day_settings'
            )
        ])

    keyboard.extend([
        [InlineKeyboardButton(get_text('help', lang), callback_data='help')],
        [InlineKeyboardButton(get_text('change_language', lang), callback_data='language')],
        [InlineKeyboardButton("📜 الشروط والأحكام" if lang == 'ar' else "📜 Terms & Conditions", callback_data='terms')]
    ])

    if is_premium:
        keyboard.extend([
            [InlineKeyboardButton(get_text('manage_currencies', lang), callback_data='manage_currencies')],
        ])
    else:
        keyboard.append([
            InlineKeyboardButton(
                "✨ Upgrade Account" if lang == 'en' else "✨ ترقية الحساب",
                callback_data='upgrade'
            )
        ])

    return InlineKeyboardMarkup(keyboard)

# تعديل دالة إضافة المؤشر
async def add_indicator(update: Update, context: CallbackContext, symbol: str, indicator_id: str):
    """إضافة مؤشر مخصص"""
    user_id = str(update.effective_user.id)

    # تحميل الإعدادات الحالية
    settings = load_user_settings(user_id)
    indicators = settings['indicators']

    # إضافة المؤشر إذا لم يكن موجوداً
    if indicator_id not in [ind['id'] for ind in indicators]:
        indicators.append({'id': indicator_id})

        # حفظ التغييرات
        if save_user_settings(user_id, indicators=indicators):
            await update.callback_query.message.reply_text(f"تم إضافة المؤشر {indicator_id} بنجاح")
        else:
            await update.callback_query.message.reply_text("حدث خطأ أثناء حفظ المؤشر")
    else:
        await update.callback_query.message.reply_text("المؤشر موجود بالفعل")

    # تحديث التحليل
    await analyze_symbol(update, context, symbol)

# تعديل دالة إضافة عملة مخصصة
async def add_custom_currency(update: Update, context: CallbackContext, symbol: str, currency: str):
    """إضافة عملة مخصصة"""
    user_id = str(update.effective_user.id)

    # تحميل الإعدادات الحالية
    settings = load_user_settings(user_id)
    currencies = settings['currencies']

    # إضافة العملة إذا لم تكن موجودة
    if currency not in currencies:
        currencies.append(currency)

        # حفظ التغييرات
        if save_user_settings(user_id, currencies=currencies):
            await update.message.reply_text(f"تم إضافة العملة {currency} بنجاح")
        else:
            await update.message.reply_text("حدث خطأ أثناء حفظ العملة")
    else:
        await update.message.reply_text("العملة موجودة بالفعل")

    # تحديث التحليل
    await analyze_symbol(update, context, symbol, target_currency=currency)

async def stop(update: Update, context: CallbackContext):
    """إيقاف البوت وإلغاء الاشتراك"""
    user_id = str(update.effective_user.id)
    user_states.pop(user_id, None)  # إزالة حالة المستخدم
    await update.message.reply_text("✅ تم إلغاء الاشتراك بنجاح. شكرًا لاستخدامك البوت!")  # رسالة تأكيد

async def error_handler(update: Update, context: CallbackContext):
    """معالجة الأخطاء"""
    logger.error(f"Exception while handling an update: {context.error}")

    try:
        # إرسال رسالة خطأ للمستخدم
        if update and update.effective_message:
            user_id = str(update.effective_user.id)
            lang = context.bot_data.get('user_settings', {}).get(user_id, {}).get('language', 'ar')

            keyboard = [[InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]]
            await update.effective_message.reply_text(
                get_text('error_occurred', lang),
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
    except:
        pass

async def remove_indicator(update: Update, context: CallbackContext, symbol: str, indicator_id: str):
    """إزالة مؤشر مخصص"""
    try:
        user_id = str(update.effective_user.id)
        lang = context.bot_data.get('user_settings', {}).get(user_id, {}).get('language', 'ar')

        # تحميل الإعدادات الحالية
        settings = load_user_settings(user_id)
        indicators = settings['indicators']

        # إزالة المؤشر
        indicators = [ind for ind in indicators if ind['id'] != indicator_id]

        # حفظ التغييرات
        if save_user_settings(user_id, indicators=indicators):
            confirmation_message = await update.callback_query.message.reply_text(
                get_text('indicator_removed', lang).format(
                    name=get_text(f'{indicator_id}_indicator', lang)
                )
            )

            # تحديث التحليل
            await analyze_symbol(update, context, symbol)

            # حذف رسالة التأكيد بعد 3 ثواني
            await asyncio.sleep(3)
            await confirmation_message.delete()
        else:
            error_message = await update.callback_query.message.reply_text(get_text('error_saving_indicator', lang))
            await asyncio.sleep(3)
            await error_message.delete()
    except Exception as e:
        logger.error(f"Error removing indicator: {str(e)}")
        await update.callback_query.message.reply_text(get_text('error_saving_indicator', lang))

async def create_analysis_text(symbol: str, market_data: dict, lang: str, user_id: str = None) -> str:
    """إنشاء نص التحليل"""
    try:
        # تحديد المؤشرات المسموح بها للمستخدم
        allowed_indicators = []
        is_subscribed = False
        if user_id:
            is_subscribed = subscription_system.is_subscribed(user_id)
            user_features = subscription_system.get_user_features(user_id)
            allowed_indicators = user_features['indicators']

        # للمستخدمين غير المشتركين، استخدام التحليل التقليدي فقط
        if not is_subscribed:
            from analysis.traditional_analysis import create_traditional_analysis
            return create_traditional_analysis(symbol, market_data, lang)

        # التحقق من وجود تفضيل لنوع التحليل (تقليدي أو ذكاء اصطناعي)
        settings = subscription_system.get_user_settings(user_id)
        analysis_type = settings.get('analysis_type', 'ai')  # القيمة الافتراضية هي 'ai'

        # للمستخدمين المشتركين، التحقق من وجود مفتاح API
        has_gemini_api = False
        try:
            has_gemini_api = await api_manager.has_api_keys(user_id, 'gemini')
        except Exception as e:
            logger.error(f"خطأ في التحقق من وجود مفتاح Gemini API: {str(e)}")
            has_gemini_api = False

        # إذا لم يكن هناك مفتاح API، استخدام التحليل التقليدي مع رسالة تنبيه
        if not has_gemini_api:
            from analysis.traditional_analysis import create_traditional_analysis
            traditional_analysis = create_traditional_analysis(symbol, market_data, lang)

            # إضافة رسالة تنبيه للمستخدم المشترك
            api_warning = "\n\n⚠️ لم يتم العثور على مفتاح Gemini API. يرجى إضافة مفتاح API للحصول على تحليل متقدم." if lang == 'ar' else "\n\n⚠️ No Gemini API key found. Please add your API key to get advanced analysis."
            return traditional_analysis + api_warning

        # التحقق من نوع التحليل المطلوب (تقليدي أو ذكاء اصطناعي)
        if analysis_type == 'traditional':
            # استخدام التحليل التقليدي إذا كان المستخدم يفضل ذلك
            from analysis.traditional_analysis import create_traditional_analysis
            return create_traditional_analysis(symbol, market_data, lang)

        # استخدام تحليل Gemini للمستخدمين المشتركين مع مفتاح API
        try:
            from analysis.gemini_analysis import get_user_api_client
            model = await get_user_api_client(user_id, 'gemini')

            if model is None:
                # في حالة فشل الحصول على نموذج Gemini، استخدام التحليل التقليدي
                from analysis.traditional_analysis import create_traditional_analysis
                traditional_analysis = create_traditional_analysis(symbol, market_data, lang)
                api_warning = "\n\n⚠️ حدث خطأ في تهيئة نموذج Gemini. يرجى المحاولة مرة أخرى لاحقًا." if lang == 'ar' else "\n\n⚠️ Error initializing Gemini model. Please try again later."
                return traditional_analysis + api_warning

            from analysis.gemini_analysis import analyze_with_gemini
            ai_analysis = await analyze_with_gemini(model, market_data, lang)

            if ai_analysis and len(ai_analysis) > 100:
                # إضافة سجل للتشخيص
                logger.info(f"تم الحصول على تحليل Gemini بنجاح باللغة {lang}")
                logger.info(f"طول التحليل: {len(ai_analysis)} حرف")
                return ai_analysis
            else:
                logger.error(f"تحليل Gemini قصير جدًا أو فارغ: {ai_analysis}")
                # في حالة فشل تحليل Gemini، استخدام التحليل التقليدي
                from analysis.traditional_analysis import create_traditional_analysis
                traditional_analysis = create_traditional_analysis(symbol, market_data, lang)

                # إضافة رسالة تنبيه
                ai_error = "\n\n⚠️ فشل في الحصول على تحليل متقدم. يرجى المحاولة مرة أخرى لاحقًا." if lang == 'ar' else "\n\n⚠️ Failed to get advanced analysis. Please try again later."
                return traditional_analysis + ai_error
        except Exception as e:
            logger.error(f"خطأ في تحليل Gemini: {str(e)}")
            # في حالة الخطأ، استخدام التحليل التقليدي
            from analysis.traditional_analysis import create_traditional_analysis
            return create_traditional_analysis(symbol, market_data, lang)

    except Exception as e:
        logger.error(f"Error creating analysis text: {str(e)}")
        return "Error creating analysis" if lang == 'en' else "خطأ في إنشاء التحليل"



async def analyze_command(update: Update, context: CallbackContext):
    """تحليل زوج عملات مع المخطط البياني"""
    user_id = str(update.effective_user.id)
    lang = context.bot_data.get('user_settings', {}).get(user_id, {}).get('language', 'ar')

    if not context.args:
        await update.message.reply_text(
            get_text('enter_symbol', lang),
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')
            ]])
        )
        return

    symbol = context.args[0].upper()
    await analyze_symbol(update, context, symbol)

async def generate_stats_report() -> str:
    """إنشاء تقرير إحصائي عن حالة النظام"""
    try:
        # جلب الإحصائيات من Firestore
        stats_ref = db.collection('stats').document('global')
        stats_doc = stats_ref.get()

        if stats_doc.exists:
            stats = stats_doc.to_dict()
        else:
            stats = {
                'total_users': 0,
                'active_subscribers': 0,
                'total_analyses': 0,
                'total_alerts': 0,
                'last_update': datetime.now().isoformat()
            }
            stats_ref.set(stats)

        # تحديث الإحصائيات
        users_ref = db.collection('user_settings')
        total_users = len(list(users_ref.get()))

        subs_ref = db.collection('subscriptions')
        active_subs = len([doc for doc in subs_ref.get() if datetime.fromisoformat(doc.to_dict()['expiry']) > datetime.now()])

        stats.update({
            'total_users': total_users,
            'active_subscribers': active_subs,
            'last_update': datetime.now().isoformat()
        })

        stats_ref.set(stats)

        # إنشاء التقرير
        report = f"""📊 *تقرير حالة النظام*

📈 *إحصائيات المستخدمين:*
• إجمالي المستخدمين: {total_users}
• المشتركين: {active_subs}
• غير المشتركين: {total_users - active_subs}

🔍 *إحصائيات النشاط:*
• إجمالي التحليلات: {stats.get('total_analyses', 0)}
• إجمالي التنبيهات: {stats.get('total_alerts', 0)}

⏰ *آخر تحديث:*
{datetime.fromisoformat(stats['last_update']).strftime('%Y-%m-%d %H:%M:%S')}"""

        return report
    except Exception as e:
        logger.error(f"Error generating stats report: {str(e)}")
        return "❌ حدث خطأ أثناء إنشاء التقرير"

# متغيرات عالمية لتتبع التغييرات
previous_stats = {
    'total_users': 0,
    'subscribed_users': 0
}

def load_previous_stats():
    """تحميل الإحصائيات السابقة من ملف subscription_data.json"""
    global previous_stats
    try:
        if os.path.exists('subscription_data.json'):
            with open('subscription_data.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                if 'stats' in data:
                    previous_stats.update(data['stats'])
    except Exception as e:
        logger.error(f"خطأ في تحميل الإحصائيات السابقة: {str(e)}")

def save_previous_stats():
    """حفظ الإحصائيات السابقة في ملف subscription_data.json"""
    try:
        with open('subscription_data.json', 'r+', encoding='utf-8') as f:
            data = json.load(f)
            data['stats'] = previous_stats
            f.seek(0)
            json.dump(data, f, ensure_ascii=False, indent=4)
            f.truncate()
    except Exception as e:
        logger.error(f"خطأ في حفظ الإحصائيات السابقة: {str(e)}")

async def manage_free_day_settings(update: Update, context: CallbackContext):
    """إدارة إعدادات اليوم المجاني"""
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # الحصول على حالة اليوم المجاني
        # نستخدم get_user_free_day_status هنا لأننا نحتاج إلى معلومات إضافية مثل اليوم المحدد
        free_day_status = free_day_system.get_user_free_day_status(user_id)
        current_day = free_day_status['free_day_of_week']
        day_name = free_day_status['day_name']
        next_free_day = free_day_status['next_free_day']
        is_active = free_day_status['is_free_day_active']

        # إنشاء نص الإعدادات
        if lang == 'ar':
            settings_text = "🎁 إعدادات اليوم المجاني\n\n"
            settings_text += f"اليوم المجاني الحالي: {day_name}\n"
            settings_text += f"اليوم المجاني القادم: {next_free_day.strftime('%Y-%m-%d')}\n"
            settings_text += f"الحالة: {'مفعل' if is_active else 'غير مفعل'}\n\n"
            settings_text += "يمكنك الاستمتاع بالميزات المدفوعة مجانًا يومًا واحدًا في الأسبوع. اختر يومك المفضل أدناه."
        else:
            settings_text = "🎁 Free Day Settings\n\n"
            settings_text += f"Current free day: {day_name}\n"
            settings_text += f"Next free day: {next_free_day.strftime('%Y-%m-%d')}\n"
            settings_text += f"Status: {'Active' if is_active else 'Inactive'}\n\n"
            settings_text += "You can enjoy premium features for free one day per week. Choose your preferred day below."

        # إنشاء أزرار اختيار اليوم
        days_ar = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
        days_en = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

        keyboard = []
        for i in range(7):
            day_text = days_en[i] if lang == 'en' else days_ar[i]
            # إضافة علامة ✓ لليوم الحالي
            if i == current_day:
                day_text += " ✓"
            keyboard.append([InlineKeyboardButton(day_text, callback_data=f'set_free_day_{i}')])

        # إضافة زر العودة
        keyboard.append([InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')])

        # إرسال الرسالة
        await update.callback_query.edit_message_text(
            text=settings_text,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        logger.error(f"خطأ في إدارة إعدادات اليوم المجاني: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ أثناء تحميل إعدادات اليوم المجاني", show_alert=True)
        await show_main_menu(update, context)

async def set_free_day(update: Update, context: CallbackContext):
    """تعيين اليوم المجاني"""
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # الحصول على اليوم المحدد
        callback_data = update.callback_query.data
        day_index = int(callback_data.replace("set_free_day_", ""))

        # تعيين اليوم المجاني
        success = await free_day_system.set_free_day(user_id, day_index)

        if success:
            # إرسال تأكيد
            days_ar = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
            days_en = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

            day_name = days_en[day_index] if lang == 'en' else days_ar[day_index]

            await update.callback_query.answer(
                f"✅ {'Free day set to ' + day_name if lang == 'en' else 'تم تعيين اليوم المجاني إلى ' + day_name}",
                show_alert=True
            )

            # إعادة عرض إعدادات اليوم المجاني
            await manage_free_day_settings(update, context)
        else:
            await update.callback_query.answer(
                "❌ " + ("Failed to set free day" if lang == 'en' else "فشل في تعيين اليوم المجاني"),
                show_alert=True
            )

    except Exception as e:
        logger.error(f"خطأ في تعيين اليوم المجاني: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ أثناء تعيين اليوم المجاني", show_alert=True)
        await show_main_menu(update, context)

async def send_daily_report(context: CallbackContext):
    """إرسال تقرير إحصائي للمستخدم المحدد عند حدوث تغييرات"""
    try:
        # تحديث الإحصائيات وفحص التغييرات
        changes = subscription_system.update_stats()

        # إرسال التقرير فقط إذا كان هناك تغييرات
        if changes:
            report = await generate_stats_report()
            changes_text = "\n".join(changes)
            full_report = f"{report}\n\n📝 *التغييرات:*\n{changes_text}"

            await context.bot.send_message(
                chat_id=7839527436,  # معرف المالك
                text=full_report,
                parse_mode=ParseMode.MARKDOWN
            )
            logger.info("تم إرسال تقرير التغييرات بنجاح إلى المالك")

    except Exception as e:
        logger.error(f"خطأ في إرسال التقرير: {str(e)}")

def encrypt_file(file_path: str, key: bytes) -> str:
    """تشفير محتويات الملف"""
    try:
        with open(file_path, 'rb') as file:
            data = file.read()
        f = Fernet(key)
        encrypted_data = f.encrypt(data)
        return base64.b64encode(encrypted_data).decode()
    except Exception as e:
        logger.error(f"خطأ في تشفير الملف: {str(e)}")
        return None

async def backup_subscription_data(context: CallbackContext):
    """نسخ احتياطي لبيانات الاشتراكات"""
    try:
        # التحقق من وجود الملف
        if not os.path.exists('subscription_data.json'):
            logger.error("ملف البيانات غير موجود")
            await context.bot.send_message(
                chat_id=7839527436,  # معرف المالك
                text="⚠️ تنبيه: ملف بيانات الاشتراكات غير موجود!"
            )
            return

        # إنشاء مفتاح تشفير
        key = Fernet.generate_key()

        # إنشاء نسخة احتياطية مع الوقت الحالي
        current_time = time.strftime("%Y%m%d_%H%M%S")
        backup_filename = f'subscription_data_{current_time}.enc'

        # تشفير الملف
        encrypted_data = encrypt_file('subscription_data.json', key)
        if encrypted_data is None:
            await context.bot.send_message(
                chat_id=7839527436,  # معرف المالك
                text="⚠️ حدث خطأ أثناء تشفير الملف"
            )
            return

        with open(backup_filename, 'w') as f:
            f.write(encrypted_data)

        # إرسال الملف المشفر إلى المالك
        with open(backup_filename, 'rb') as file:
            await context.bot.send_document(
                chat_id=7839527436,  # معرف المالك
                document=file,
                caption=f"🔐 نسخة احتياطية مشفرة - {current_time}\n⚠️ هذا الملف مشفر ولا يمكن قراءته إلا بواسطة النظام"
            )

        # إرسال مفتاح التشفير في رسالة منفصلة
        await context.bot.send_message(
            chat_id=7839527436,  # معرف المالك
            text=f"🔑 مفتاح التشفير (يرجى الاحتفاظ به بأمان):\n`{key.decode()}`",
            parse_mode=ParseMode.MARKDOWN
        )

        # إرسال التقرير
        report = await generate_stats_report()
        await context.bot.send_message(
            chat_id=7839527436,  # معرف المالك
            text=report,
            parse_mode=ParseMode.MARKDOWN
        )

        # حذف الملف المؤقت
        os.remove(backup_filename)
        logger.info(f"تم إنشاء وإرسال نسخة احتياطية مشفرة بنجاح - {current_time}")

    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        await context.bot.send_message(
            chat_id=7839527436,  # معرف المالك
            text=f"⚠️ خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
        )

async def test_subscription(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """اختبار سريع لحالة الاشتراك"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من حالة الاشتراك
        subscription_status = subscription_system.is_subscribed(user_id)
        free_usage = subscription_system.get_free_usage(user_id)
        subscription_info = subscription_system.get_subscription_info(user_id)

        # إنشاء رسالة التقرير
        report = "📊 *تقرير حالة الاشتراك*\n\n"

        if subscription_status:
            report += "✅ *حالة الاشتراك:* مشترك\n"
            report += f"📅 *تاريخ الانتهاء:* {subscription_info['expiry_date']}\n"
            report += "🌟 *المزايا المتاحة:*\n"
            report += "  • تحليلات غير محدودة\n"
            report += "  • تنبيهات غير محدودة\n"
            report += "  • جميع المؤشرات الفنية\n"
            report += "  • جميع أزواج العملات\n"
        else:
            report += "❌ *حالة الاشتراك:* غير مشترك\n"
            report += "*الاستخدام المجاني المتبقي اليوم:*\n"
            report += f"📊 تحليلات متبقية: {free_usage['analyses']}/3\n"
            report += f"🔔 تنبيهات متبقية: {free_usage['alerts']}/1\n"
            report += "\n💡 *للحصول على مزايا غير محدودة، يرجى الاشتراك*"

        await update.message.reply_text(report, parse_mode=ParseMode.MARKDOWN)
    except Exception as e:
        logger.error(f"Error in test_subscription: {str(e)}")
        await update.message.reply_text("❌ حدث خطأ أثناء فحص حالة الاشتراك. الرجاء المحاولة مرة أخرى.")

async def cast(update: Update, context: CallbackContext):
    """أمر إرسال رسالة جماعية لجميع المستخدمين"""
    # التحقق من أن المستخدم هو المطور
    if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
        await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
        logger.warning(f"محاولة غير مصرح بها لإرسال رسالة جماعية من قبل المستخدم {update.effective_user.id}")
        return

    if not context.args:
        await update.message.reply_text("الرجاء إدخال نص الرسالة بعد الأمر.")
        return

    # تجميع الرسالة من الوسائط
    message = ' '.join(context.args)

    # تنظيف الرسالة من أي علامات خاصة غير مرغوبة
    # إزالة علامات <|im_start|> و <|im_end|> التي قد تظهر من نموذج الذكاء الاصطناعي
    message = message.replace('<|im_start|>', '').replace('<|im_end|>', '')

    # التأكد من عدم وجود أي علامات خاصة أخرى قد تسبب مشاكل في التنسيق
    message = message.strip()
    success_count = 0
    fail_count = 0
    format_error_count = 0
    inactive_users = []  # قائمة المستخدمين غير النشطين (حظروا البوت أو حذفوا المحادثة)

    # إرسال رسالة تأكيد للمطور
    confirm_msg = await update.message.reply_text("جاري إرسال الرسالة الجماعية... يرجى الانتظار.")

    # قراءة قائمة المستخدمين من Firestore
    users_ref = db.collection('users')
    # استثناء المستخدمين المحظورين والغير نشطين
    # نستخدم استعلام بسيط لأن Firestore لا يدعم استعلامات OR المعقدة
    users = users_ref.stream()

    # تحويل المستخدمين إلى قائمة لمعرفة العدد الإجمالي
    user_list = []
    for user in users:
        user_id = user.id
        user_data = user.to_dict()

        # تخطي المستخدمين غير الصالحين
        if not user_id or not user_id.isdigit() or user_id.startswith('_'):
            logger.info(f"تخطي معرف مستخدم غير صالح: {user_id}")
            continue

        # تخطي المستخدمين المحظورين والغير نشطين
        status = user_data.get('status', '')
        if status == 'banned' or status == 'inactive':
            logger.info(f"تخطي المستخدم {user_id} بسبب الحالة: {status}")
            continue

        user_list.append(user)

    total_users = len(user_list)

    if total_users == 0:
        await confirm_msg.edit_text("لا يوجد مستخدمين نشطين لإرسال الرسالة إليهم.")
        return

    await confirm_msg.edit_text(f"جاري إرسال الرسالة الجماعية إلى {total_users} مستخدم... يرجى الانتظار.")

    for user in user_list:
        user_data = user.to_dict()
        user_id = user.id

        try:
            # محاولة إرسال الرسالة مع تنسيق Markdown أولاً (أكثر شيوعًا في الرسائل الجماعية)
            try:
                # تنظيف الرسالة وإعدادها للإرسال
                clean_message = message.strip()

                # محاولة إرسال الرسالة بتنسيق Markdown
                await context.bot.send_message(
                    chat_id=int(user_id),  # تحويل المعرف إلى رقم
                    text=clean_message,
                    parse_mode=ParseMode.MARKDOWN
                )
                success_count += 1
            except telegram.error.BadRequest as md_error:
                try:
                    # إذا فشل Markdown، نحاول HTML
                    logger.info(f"محاولة استخدام HTML للمستخدم {user_id}")
                    await context.bot.send_message(
                        chat_id=int(user_id),
                        text=clean_message,
                        parse_mode=ParseMode.HTML
                    )
                    success_count += 1
                except telegram.error.BadRequest as html_error:
                    # إذا فشلت جميع التنسيقات، نرسل بدون تنسيق
                    logger.warning(f"فشلت جميع التنسيقات للمستخدم {user_id}، إرسال بدون تنسيق")
                    # تنظيف الرسالة من علامات التنسيق لإرسالها بدون تنسيق
                    plain_message = clean_message
                    # إزالة علامات التنسيق المحتملة
                    for char in ['*', '_', '`', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']:
                        plain_message = plain_message.replace(char, ' ' + char + ' ')
                    plain_message = ' '.join(plain_message.split())  # تنظيف المسافات المتعددة

                    await context.bot.send_message(
                        chat_id=int(user_id),
                        text=plain_message,
                        parse_mode=None  # بدون تنسيق
                    )
                    success_count += 1
                    format_error_count += 1

            await asyncio.sleep(0.1)  # تأخير صغير لتجنب تجاوز حدود API

        except telegram.error.Forbidden:
            logger.info(f"المستخدم {user_id} قام بحظر البوت")
            inactive_users.append(user_id)
            # لا نزيد عداد الفشل لأن هذا ليس فشلاً حقيقياً، بل حالة طبيعية

        except telegram.error.BadRequest as e:
            if "chat not found" in str(e).lower():
                logger.info(f"لم يتم العثور على الدردشة للمستخدم {user_id}")
                inactive_users.append(user_id)
                # لا نزيد عداد الفشل لأن هذا ليس فشلاً حقيقياً، بل حالة طبيعية
            else:
                logger.error(f"خطأ في إرسال الرسالة للمستخدم {user_id}: {str(e)}")
                fail_count += 1

        except Exception as e:
            logger.error(f"خطأ غير متوقع في إرسال الرسالة للمستخدم {user_id}: {str(e)}")
            fail_count += 1

        # تحديث رسالة التقدم كل 10 مستخدمين
        if (success_count + fail_count + len(inactive_users)) % 10 == 0:
            progress = (success_count + fail_count + len(inactive_users)) / total_users * 100
            await confirm_msg.edit_text(
                f"جاري إرسال الرسالة الجماعية... {progress:.1f}%\n"
                f"✅ نجح: {success_count}\n"
                f"❌ فشل: {fail_count}\n"
                f"⚠️ غير نشط: {len(inactive_users)}"
            )

    # تحديث حالة المستخدمين غير النشطين في قاعدة البيانات
    for inactive_user_id in inactive_users:
        try:
            user_ref = db.collection('users').document(inactive_user_id)
            user_doc = user_ref.get()

            if user_doc.exists:
                # تحديث الوثيقة الموجودة
                user_ref.update({
                    'status': 'inactive',
                    'lastUpdated': datetime.now().isoformat()
                })
                logger.info(f"تم تحديث حالة المستخدم {inactive_user_id} إلى غير نشط")
            else:
                # إنشاء وثيقة جديدة إذا لم تكن موجودة
                user_ref.set({
                    'userId': inactive_user_id,
                    'status': 'inactive',
                    'lastUpdated': datetime.now().isoformat(),
                    'createdAt': datetime.now().isoformat()
                })
                logger.info(f"تم إنشاء وثيقة جديدة للمستخدم {inactive_user_id} بحالة غير نشط")
        except Exception as e:
            logger.error(f"خطأ في تحديث حالة المستخدم {inactive_user_id}: {str(e)}")

    # إنشاء رسالة الحالة النهائية
    active_users = total_users - len(inactive_users)
    status_message = f"""
تم إرسال الرسالة الجماعية:
✅ نجح: {success_count} ({format_error_count} منها تم إرسالها بدون تنسيق)
❌ فشل: {fail_count}
⚠️ غير نشط: {len(inactive_users)}
📊 إجمالي المستخدمين النشطين: {active_users}
"""
    await confirm_msg.edit_text(status_message)

    # إرسال رسالة تقرير إضافية للمطور فقط
    if len(inactive_users) > 0:
        inactive_report = f"تم تحديث حالة {len(inactive_users)} مستخدم إلى 'غير نشط' لأنهم قاموا بحظر البوت أو حذف المحادثة."
        await update.message.reply_text(inactive_report)

async def ban_user(update: Update, context: CallbackContext):
    """أمر حظر مستخدم من استخدام البوت"""
    # التحقق من أن المستخدم هو المطور
    if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
        await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
        logger.warning(f"محاولة غير مصرح بها لحظر مستخدم من قبل المستخدم {update.effective_user.id}")
        return

    if not context.args:
        await update.message.reply_text("الرجاء إدخال معرف المستخدم المراد حظره.")
        return

    user_id = context.args[0]

    try:
        # إضافة المستخدم إلى قائمة المحظورين
        banned_users_ref = db.collection('banned_users').document(user_id)
        banned_users_ref.set({
            'banned_at': datetime.now().isoformat(),
            'banned_by': str(update.effective_user.id),
            'status': 'banned'
        })

        # تحديث حالة المستخدم في جدول users إن وجد
        users_ref = db.collection('users').document(user_id)
        user_data = users_ref.get()
        if user_data.exists:
            users_ref.update({
                'status': 'banned',
                'lastUpdated': datetime.now().isoformat()
            })

        logger.info(f"تم حظر المستخدم {user_id} بنجاح")
        await update.message.reply_text(f"تم حظر المستخدم {user_id} بنجاح.")
    except Exception as e:
        logger.error(f"خطأ في حظر المستخدم {user_id}: {str(e)}")
        await update.message.reply_text(f"حدث خطأ أثناء حظر المستخدم: {str(e)}")

async def unban_user(update: Update, context: CallbackContext):
    """أمر إلغاء حظر مستخدم"""
    # التحقق من أن المستخدم هو المطور
    if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
        await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
        logger.warning(f"محاولة غير مصرح بها لإلغاء حظر مستخدم من قبل المستخدم {update.effective_user.id}")
        return

    if not context.args:
        await update.message.reply_text("الرجاء إدخال معرف المستخدم المراد إلغاء حظره.")
        return

    user_id = context.args[0]

    try:
        # حذف المستخدم من قائمة المحظورين
        banned_users_ref = db.collection('banned_users').document(user_id)
        banned_user_data = banned_users_ref.get()

        if not banned_user_data.exists:
            await update.message.reply_text(f"المستخدم {user_id} غير موجود في قائمة المحظورين.")
            return

        banned_users_ref.delete()

        # تحديث حالة المستخدم في جدول users إن وجد
        users_ref = db.collection('users').document(user_id)
        user_data = users_ref.get()
        if user_data.exists:
            users_ref.update({
                'status': 'active',
                'lastUpdated': datetime.now().isoformat()
            })

        logger.info(f"تم إلغاء حظر المستخدم {user_id} بنجاح")
        await update.message.reply_text(f"تم إلغاء حظر المستخدم {user_id} بنجاح.")
    except Exception as e:
        logger.error(f"خطأ في إلغاء حظر المستخدم {user_id}: {str(e)}")
        await update.message.reply_text(f"حدث خطأ أثناء إلغاء حظر المستخدم: {str(e)}")

async def system_info(update: Update, context: CallbackContext):
    """أمر عرض معلومات النظام"""
    # التحقق من أن المستخدم هو المطور
    if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
        await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
        logger.warning(f"محاولة غير مصرح بها لعرض معلومات النظام من قبل المستخدم {update.effective_user.id}")
        return

    # التحقق من اتصال Firestore
    firestore_status = "✅ متصل" if check_firestore_connection() else "❌ غير متصل"

    # التحقق من اتصال Firestore Cache
    cache_status = "✅ متصل"
    try:
        # اختبار بسيط للتخزين المؤقت
        test_key = f"system_test_{datetime.now().timestamp()}"
        firestore_cache.set(test_key, {"test": True}, ex=60, cache_type="system_data")
        test_result = firestore_cache.get(test_key, cache_type="system_data")
        if not test_result or not test_result.get("test"):
            cache_status = "❌ غير متصل"
        # حذف بيانات الاختبار
        firestore_cache.delete(test_key, cache_type="system_data")
    except Exception as e:
        logger.error(f"خطأ في اختبار التخزين المؤقت: {str(e)}")
        cache_status = "❌ غير متصل"

    # جمع إحصائيات النظام
    db = firestore.client()
    users_ref = db.collection('users')
    total_users = 0
    active_subscribers = 0

    for doc in users_ref.get():
        if doc.id.startswith('_'):  # تجاهل الوثائق الوصفية
            continue
        total_users += 1
        user_data = doc.to_dict()
        if user_data.get('subscriptionStatus') == 'مشترك':
            try:
                expiry = datetime.fromisoformat(user_data.get('subscriptionExpiry'))
                if expiry > datetime.now():
                    active_subscribers += 1
            except (KeyError, ValueError):
                logger.warning(f"تحذير: مفتاح 'subscriptionExpiry' غير موجود أو تنسيقه غير صحيح للمستخدم {doc.id}")

    # حساب التنبيهات النشطة
    alerts_ref = db.collection('alerts')
    total_alerts = len([doc for doc in alerts_ref.stream() if not doc.id.startswith('_')])

    # حساب عدد البيانات المؤقتة
    cache_stats = {
        "market_data": 0,
        "user_data": 0,
        "system_data": 0
    }

    try:
        for cache_type in cache_stats.keys():
            collection_name = firestore_cache._get_collection(cache_type)
            docs = db.collection(collection_name).get()
            cache_stats[cache_type] = len(list(docs)) - 1  # نقص 1 للوثيقة التهيئية
            if cache_stats[cache_type] < 0:
                cache_stats[cache_type] = 0
    except Exception as e:
        logger.error(f"خطأ في حساب إحصائيات التخزين المؤقت: {str(e)}")

    total_cache_items = sum(cache_stats.values())

    system_info_text = f"""
🖥️ معلومات النظام:

📡 حالة الاتصال:
• Firestore: {firestore_status}
• Firestore Cache: {cache_status}

📊 إحصائيات:
• إجمالي المستخدمين: {total_users}
• المشتركين النشطين: {active_subscribers}
• التنبيهات النشطة: {total_alerts}

💾 إحصائيات التخزين المؤقت:
• بيانات السوق: {cache_stats['market_data']}
• بيانات المستخدمين: {cache_stats['user_data']}
• بيانات النظام: {cache_stats['system_data']}
• إجمالي العناصر: {total_cache_items}

⚙️ معلومات التكوين:
• إصدار النظام: 1.1.0
• وقت التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    await update.message.reply_text(system_info_text)

async def grant_free_day_command(update: Update, context: CallbackContext):
    """منح يوم مجاني للمستخدمين (للمطور فقط)"""
    try:
        # التحقق من أن المستخدم هو المطور
        if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
            await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
            logger.warning(f"محاولة غير مصرح بها لمنح يوم مجاني من قبل المستخدم {update.effective_user.id}")
            return

        # التحقق من وجود المعاملات
        if not context.args or len(context.args) == 0:
            await update.message.reply_text(
                "❌ الرجاء تحديد معرف المستخدم أو 'all' لمنح يوم مجاني لجميع المستخدمين غير المشتركين\n"
                "مثال: /free_day 123456789\n"
                "مثال: /free_day all"
            )
            return

        target = context.args[0].lower()

        # تحديد مدة اليوم المجاني (اختياري)
        duration_hours = 24
        if len(context.args) > 1:
            try:
                duration_hours = int(context.args[1])
                if duration_hours <= 0:
                    await update.message.reply_text("❌ يجب أن تكون المدة أكبر من صفر")
                    return
            except ValueError:
                await update.message.reply_text("❌ يجب أن تكون المدة عدداً صحيحاً")
                return

        # منح يوم مجاني لجميع المستخدمين غير المشتركين
        if target == 'all':
            await update.message.reply_text("⏳ جاري منح يوم مجاني لجميع المستخدمين غير المشتركين...")
            stats = free_day_system.grant_free_day_to_all(duration_hours)

            result_message = (
                f"✅ تم منح يوم مجاني بنجاح!\n\n"
                f"📊 الإحصائيات:\n"
                f"• إجمالي المستخدمين: {stats['total']}\n"
                f"• تم المنح بنجاح: {stats['success']}\n"
                f"• فشل المنح: {stats['failed']}\n"
                f"• تم تخطي المشتركين: {stats['skipped_subscribers']}\n"
                f"• مدة اليوم المجاني: {duration_hours} ساعة"
            )

            await update.message.reply_text(result_message)
            logger.info(f"تم منح يوم مجاني لجميع المستخدمين غير المشتركين بواسطة المطور {update.effective_user.id}")
            return

        # منح يوم مجاني لمستخدم محدد
        user_id = target
        success = free_day_system.grant_free_day(user_id, duration_hours)

        if success:
            await update.message.reply_text(
                f"✅ تم منح يوم مجاني للمستخدم {user_id} بنجاح!\n"
                f"• مدة اليوم المجاني: {duration_hours} ساعة"
            )
            logger.info(f"تم منح يوم مجاني للمستخدم {user_id} بواسطة المطور {update.effective_user.id}")
        else:
            await update.message.reply_text(f"❌ فشل في منح يوم مجاني للمستخدم {user_id}")
            logger.error(f"فشل في منح يوم مجاني للمستخدم {user_id}")

    except Exception as e:
        logger.error(f"خطأ في تنفيذ أمر منح يوم مجاني: {str(e)}")
        await update.message.reply_text(f"❌ حدث خطأ: {str(e)}")

async def cleanup_system(update: Update, context: CallbackContext):
    """أمر تنظيف النظام من البيانات القديمة"""
    # التحقق من أن المستخدم هو المطور
    if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
        await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
        logger.warning(f"محاولة غير مصرح بها لتنظيف النظام من قبل المستخدم {update.effective_user.id}")
        return

    try:
        # تنظيف المعاملات الفاشلة
        transaction_manager = TransactionManager()
        await transaction_manager.cleanup_failed_transactions()

        # تنظيف النسخ الاحتياطية القديمة
        github_backup = GitHubBackup()
        await github_backup.cleanup_old_backups()

        # تنظيف التنبيهات منتهية الصلاحية
        db = firestore.client()
        alerts_ref = db.collection('alerts')
        expired_alerts = alerts_ref.where(filter=FieldFilter('expiry', '<', datetime.now())).stream()
        for alert in expired_alerts:
            alert.reference.delete()

        # تنظيف البيانات المؤقتة منتهية الصلاحية
        deleted_count = firestore_cache.clear_expired()
        logger.info(f"تم حذف {deleted_count} من البيانات المؤقتة منتهية الصلاحية")

        await update.message.reply_text(f"تم تنظيف النظام بنجاح! ✨\nتم حذف {deleted_count} من البيانات المؤقتة منتهية الصلاحية")
    except Exception as e:
        await update.message.reply_text(f"حدث خطأ أثناء تنظيف النظام: {str(e)}")

async def backup_data(context: CallbackContext):
    """نسخ احتياطي للبيانات"""
    try:
        # إنشاء مجموعة البيانات
        backup_data = {
            'timestamp': datetime.now().isoformat(),
            'collections': {}
        }

        # نسخ جميع المجموعات
        collections = [
            'user_settings',
            'subscriptions',
            'alerts',
            'stats',
            'config'
        ]

        for collection_name in collections:
            collection_ref = db.collection(collection_name)
            docs = collection_ref.get()

            backup_data['collections'][collection_name] = {
                doc.id: doc.to_dict()
                for doc in docs
            }

        # تشفير البيانات
        key = Fernet.generate_key()
        f = Fernet(key)
        encrypted_data = f.encrypt(json.dumps(backup_data).encode())

        # إنشاء ملف النسخة الاحتياطية
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f'backup_{current_time}.enc'

        with open(backup_filename, 'wb') as f:
            f.write(encrypted_data)

        # إرسال الملف والمفتاح إلى المالك
        with open(backup_filename, 'rb') as file:
            await context.bot.send_document(
                chat_id=SystemConfig.DEVELOPER_ID,
                document=file,
                caption=f"🔐 نسخة احتياطية مشفرة - {current_time}"
            )

        await context.bot.send_message(
            chat_id=SystemConfig.DEVELOPER_ID,
            text=f"🔑 مفتاح التشفير (يرجى الاحتفاظ به بأمان):\n`{key.decode()}`",
            parse_mode=ParseMode.MARKDOWN
        )

        # إرسال تقرير الإحصائيات
        report = await generate_stats_report()
        await context.bot.send_message(
            chat_id=SystemConfig.DEVELOPER_ID,
            text=report,
            parse_mode=ParseMode.MARKDOWN
        )

        # حذف الملف المؤقت
        os.remove(backup_filename)
        logger.info(f"تم إنشاء وإرسال نسخة احتياطية مشفرة بنجاح - {current_time}")

    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        await context.bot.send_message(
            chat_id=SystemConfig.DEVELOPER_ID,
            text=f"⚠️ خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
        )

async def initialize_firestore():
    """تهيئة قاعدة البيانات في Firestore"""
    try:
        # التحقق من وجود المجموعات
        existing_collections = [collection.id for collection in db.collections()]

        # تكوين النظام الأساسي
        system_config = {
            '_metadata': {
                'version': '1.0',
                'last_update': datetime.now().isoformat(),
                'initialized': True
            },
            '_schema': {
                'users': {
                    'userId': 'string',
                    'username': 'string',
                    'chatId': 'number',
                    'subscriptionStatus': 'string',
                    'subscriptionExpiry': 'timestamp',
                    'language': 'string',
                    'createdAt': 'timestamp'
                },
                'transactions': {
                    'userId': 'string',
                    'amount': 'number',
                    'status': 'string',
                    'createdAt': 'timestamp'
                },
                'alerts': {
                    'userId': 'string',
                    'symbol': 'string',
                    'price': 'number',
                    'condition': 'string',
                    'createdAt': 'timestamp'
                },
                'user_settings': {
                    'userId': 'string',
                    'language': 'string'
                }
            }
        }

        # إنشاء أو تحديث تكوين النظام
        config_ref = db.collection('_system').document('config')
        if not config_ref.get().exists:
            config_ref.set(system_config)
            logger.info("تم إنشاء تكوين النظام")

        # المجموعات الأساسية التي نحتاج لإنشائها
        required_collections = [
            'users',
            'transactions',
            'subscriptions',
            'alerts',
            'user_settings'       # إعدادات المستخدم
        ]

        # إنشاء المجموعات الناقصة
        for collection_name in required_collections:
            if collection_name not in existing_collections:
                # إنشاء وثيقة تهيئة للمجموعة
                init_doc = {
                    '_metadata': {
                        'created_at': datetime.now().isoformat(),
                        'collection_type': collection_name
                    }
                }
                db.collection(collection_name).document('_init').set(init_doc)
                logger.info(f"تم إنشاء مجموعة {collection_name}")

        # تحديث حالة التهيئة
        config_ref.update({
            '_metadata.last_update': datetime.now().isoformat(),
            '_metadata.collections': required_collections
        })

        logger.info("✅ تم تهيئة قاعدة البيانات بنجاح")
        return True

    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة قاعدة البيانات: {str(e)}")
        return False

def check_firestore_connection():
    """التحقق من الاتصال بـ Firestore"""
    try:
        # التحقق مباشرة من الاتصال باستخدام كائن db المهيأ مسبقًا
        if not db:
            raise Exception("كائن قاعدة بيانات Firestore غير مهيأ")
            
        logger.info("🔄 جاري التحقق من الاتصال بـ Firestore باستخدام كائن db المهيأ...")
        
        # التحقق من الاتصال
        test_ref = db.collection('test').document('connection')

        # اختبار الكتابة
        test_data = {
            'test': 'connection',
            'timestamp': datetime.now().isoformat(),
            'random_id': str(uuid.uuid4())
        }
        logger.info("📝 جاري اختبار الكتابة في Firestore...")
        test_ref.set(test_data)

        # اختبار القراءة
        logger.info("📖 جاري اختبار القراءة من Firestore...")
        read_data = test_ref.get().to_dict()
        if read_data != test_data:
            raise Exception("فشل في مطابقة البيانات المقروءة مع البيانات المكتوبة")

        # حذف بيانات الاختبار
        logger.info("🗑️ جاري تنظيف بيانات الاختبار...")
        test_ref.delete()

        logger.info("✨ تم التحقق من جميع عمليات Firestore بنجاح")
        return True
    except Exception as e:
        logger.error(f"❌ خطأ في الاتصال بـ Firestore: {str(e)}")
        return False

async def initialize_system():
    """تهيئة النظام وفحص الاتصال"""
    global encryption_key, api_manager
    try:
        logger.info("🔄 جاري تهيئة النظام...")

        # التحقق من الاتصال بـ Firestore
        logger.info("🔍 جاري التحقق من الاتصال بـ Firestore...")
        if not check_firestore_connection():
            raise Exception("فشل في الاتصال بـ Firestore")

        # تهيئة قاعدة البيانات
        logger.info("🔧 جاري تهيئة قاعدة البيانات...")
        if not await initialize_firestore():
            raise Exception("فشل في تهيئة قاعدة البيانات")

        # تهيئة مفتاح التشفير
        logger.info("🔑 جاري تهيئة مفتاح التشفير...")
        encryption_key = await get_or_create_encryption_key()

        # تهيئة مدير API
        logger.info("💻 جاري تهيئة مدير API...")
        global api_manager
        api_manager = APIManager(db, encryption_key)

        logger.info("✅ تم تهيئة النظام بنجاح")
        return True
    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة النظام: {str(e)}")
        return False

async def cancel_transaction(transaction_id: str, user_id: str) -> bool:
    """إلغاء المعاملة وتحديث حالتها في Firestore"""
    try:
        transaction_ref = db.collection('transactions').document(transaction_id)
        transaction_data = transaction_ref.get()

        if transaction_data.exists:
            transaction = transaction_data.to_dict()
            if transaction['user_id'] == user_id:
                # تحديث حالة المعاملة
                transaction_ref.update({
                    'status': 'cancelled',
                    'cancelled_at': datetime.now().isoformat(),
                    'used': False
                })

                # حذف المعاملة بعد التحديث
                transaction_ref.delete()

                logger.info(f"تم إلغاء وحذف المعاملة {transaction_id} بنجاح")
                return True
        return False
    except Exception as e:
        logger.error(f"خطأ في إلغاء المعاملة {transaction_id}: {str(e)}")
        return False

class TransactionManager:
    def __init__(self):
        self.cleanup_scheduler = AsyncIOScheduler(timezone=pytz.UTC)
        self.cleanup_scheduler.add_job(
            self.cleanup_failed_transactions,
            'interval',
            minutes=30
        )

    async def initialize(self):
        """تهيئة مدير المعاملات وبدء جدولة التنظيف"""
        if not self.cleanup_scheduler.running:
            self.cleanup_scheduler.start()

    async def mark_transaction_failed(self, transaction_id: str, reason: str = None):
        """تحديد المعاملة كفاشلة"""
        try:
            transaction_ref = db.collection('transactions').document(transaction_id)
            transaction_data = transaction_ref.get()

            if not transaction_data.exists:
                logger.error(f"المعاملة {transaction_id} غير موجودة")
                return

            # تحديث حالة المعاملة
            update_data = {
                'status': 'failed',
                'failed_at': datetime.now().isoformat(),
                'failure_reason': reason
            }

            if transaction_data.to_dict():
                update_data['retry_count'] = transaction_data.to_dict().get('retry_count', 0) + 1

            transaction_ref.update(update_data)
            logger.info(f"❌ تم تحديد المعاملة {transaction_id} كفاشلة")

        except Exception as e:
            logger.error(f"Error marking transaction as failed: {str(e)}")

    async def cleanup_failed_transactions(self):
        """تنظيف المعاملات الفاشلة بشكل دوري"""
        try:
            logger.info(f"⏰ بدء عملية التنظيف الدوري في {datetime.now().isoformat()}")

            transactions_ref = db.collection('transactions')
            # استخدام FieldFilter لتجنب التحذير
            failed_transactions = transactions_ref.where(
                filter=FieldFilter('status', '==', 'failed')
            ).get()

            deleted_count = 0
            for transaction in failed_transactions:
                try:
                    if transaction.id == '_metadata':
                        continue

                    # التحقق من حالة المعاملة قبل الحذف
                    transaction_data = transaction.to_dict()
                    if transaction_data.get('status') != 'failed':
                        logger.warning(f"⚠️ المعاملة {transaction.id} ليست فاشلة. الحالة الحالية: {transaction_data.get('status')}")
                        continue

                    # حذف المعاملة
                    transaction.reference.delete()
                    deleted_count += 1
                    logger.info(f"✅ تم حذف المعاملة الفاشلة: {transaction.id}")

                except Exception as tx_error:
                    logger.error(f"❌ خطأ في حذف المعاملة {transaction.id}: {str(tx_error)}")
                    continue

            if deleted_count > 0:
                logger.info(f"🗑️ تم حذف {deleted_count} معاملة فاشلة")
            else:
                logger.info("⚠️ لا توجد معاملات فاشلة للحذف")

            logger.info(f"⏰ انتهاء عملية التنظيف الدوري في {datetime.now().isoformat()}")

        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف المعاملات الفاشلة: {str(e)}")

    async def mark_transaction_failed(self, transaction_id: str, reason: str = None):
        """تحديد المعاملة كفاشلة وحذفها مباشرةً"""
        try:
            transaction_ref = db.collection('transactions').document(transaction_id)
            transaction_data = transaction_ref.get()

            if not transaction_data.exists:
                logger.error(f"المعاملة {transaction_id} غير موجودة")
                return

            # تحديث حالة المعاملة
            update_data = {
                'status': 'failed',
                'failed_at': datetime.now().isoformat(),
                'failure_reason': reason
            }

            if transaction_data.to_dict():
                update_data['retry_count'] = transaction_data.to_dict().get('retry_count', 0) + 1

            # تحديث الحالة ثم الحذف
            transaction_ref.update(update_data)
            transaction_ref.delete()

            logger.info(f"❌ تم تحديد وحذف المعاملة {transaction_id} كفاشلة")

        except Exception as e:
            logger.error(f"Error marking and deleting failed transaction: {str(e)}")

# تهيئة مدير المعاملات
transaction_manager = TransactionManager()

async def delete_message_after_delay(bot, chat_id, message_id, delay_seconds):
    """حذف رسالة بعد فترة زمنية محددة"""
    try:
        await asyncio.sleep(delay_seconds)
        await bot.delete_message(chat_id=chat_id, message_id=message_id)
        logger.info(f"تم حذف الرسالة {message_id} بعد {delay_seconds} ثانية")
    except Exception as e:
        logger.error(f"خطأ في حذف الرسالة: {str(e)}")

async def handle_payment_verification(update: Update, context: CallbackContext):
    """معالجة التحقق من الدفع"""
    try:
        user_id = str(update.effective_user.id)
        query = update.callback_query
        callback_data = query.data
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # التحقق من وجود معرف معاملة محدد في callback_data
        order_id = None
        if callback_data.startswith('verify_payment_'):
            order_id = callback_data.split('_')[2]
            logger.info(f"التحقق من معاملة محددة: {order_id} للمستخدم {user_id}")

        # إرسال رسالة انتظار
        await query.answer(
            "جاري التحقق من الدفع..." if lang == 'ar' else
            "Verifying payment...",
            show_alert=False
        )

        # إرسال رسالة انتظار
        wait_message = await query.edit_message_text(
            "⏳ جاري التحقق من الدفع... يرجى الانتظار" if lang == 'ar' else
            "⏳ Verifying payment... Please wait"
        )

        # إذا كان هناك معرف معاملة محدد، نتحقق منه مباشرة
        if order_id:
            # التحقق من المعاملة في قاعدة البيانات
            transaction_ref = db.collection('transactions').document(order_id)
            transaction_data = transaction_ref.get()

            if not transaction_data.exists:
                logger.warning(f"المعاملة {order_id} غير موجودة في قاعدة البيانات")
                # إنشاء رسالة خطأ
                error_message = (
                    "⚠️ لم يتم العثور على المعاملة المحددة. يرجى المحاولة مرة أخرى." if lang == 'ar' else
                    "⚠️ The specified transaction was not found. Please try again."
                )

                await wait_message.edit_text(
                    error_message,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton(
                            "🔍 التحقق مرة أخرى" if lang == 'ar' else "🔍 Verify Again",
                            callback_data='verify_payment'
                        )],
                        [InlineKeyboardButton(
                            get_text('back_to_main', lang),
                            callback_data='back_to_main'
                        )]
                    ])
                )
                return

            # التحقق من أن المعاملة تخص المستخدم الحالي
            transaction = transaction_data.to_dict()
            if transaction.get('user_id') != user_id:
                logger.warning(f"المعاملة {order_id} تخص مستخدم آخر")
                # إنشاء رسالة خطأ
                error_message = (
                    "⚠️ هذه المعاملة تخص مستخدم آخر. يرجى التحقق من المعاملة الصحيحة." if lang == 'ar' else
                    "⚠️ This transaction belongs to another user. Please check the correct transaction."
                )

                await wait_message.edit_text(
                    error_message,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton(
                            "🔍 التحقق مرة أخرى" if lang == 'ar' else "🔍 Verify Again",
                            callback_data='verify_payment'
                        )],
                        [InlineKeyboardButton(
                            get_text('back_to_main', lang),
                            callback_data='back_to_main'
                        )]
                    ])
                )
                return

            # التحقق من حالة المعاملة
            if transaction.get('status') == 'completed' and transaction.get('used', False):
                logger.info(f"المعاملة {order_id} تم استخدامها بالفعل")
                # إنشاء رسالة نجاح
                success_message = (
                    "✅ تم تأكيد الدفع بنجاح!\n\n"
                    "تم تفعيل اشتراكك وبإمكانك الآن الاستمتاع بجميع الميزات المتقدمة."
                ) if lang == 'ar' else (
                    "✅ Payment confirmed successfully!\n\n"
                    "Your subscription has been activated and you can now enjoy all advanced features."
                )

                await wait_message.edit_text(
                    success_message,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton(
                            get_text('back_to_main', lang),
                            callback_data='back_to_main'
                        )]
                    ])
                )
                return

        # إنشاء معاملة جديدة إذا لم يكن هناك معرف معاملة محدد
        if not order_id:
            transaction_data = {
                'user_id': user_id,
                'amount': 5.0,  # USD
                'created_at': datetime.now().isoformat(),
                'status': 'pending',
                'used': False,
                'expires_at': (datetime.now() + timedelta(hours=1)).isoformat(),
                'verification_attempts': 0,
                'payment_method': 'paypal'
            }

            # حفظ المعاملة في Firestore
            transaction_ref = db.collection('transactions').document()
            transaction_ref.set(transaction_data)
            transaction_id = transaction_ref.id
        else:
            transaction_id = order_id
            # تحديث عدد محاولات التحقق
            transaction_ref = db.collection('transactions').document(transaction_id)
            transaction_data = transaction_ref.get().to_dict()
            verification_attempts = transaction_data.get('verification_attempts', 0) + 1
            transaction_ref.update({
                'verification_attempts': verification_attempts,
                'last_verification': datetime.now().isoformat()
            })

        # التحقق من المعاملة في PayPal
        verification_result = await verify_paypal_transaction(user_id, amount=5.0, transaction_id=transaction_id)

        if verification_result:
            # تفعيل الاشتراك
            if await activate_subscription(user_id, transaction_id):
                # إرسال رسالة نجاح
                success_message = (
                    "✅ تم تأكيد الدفع بنجاح!\n\n"
                    "تم تفعيل اشتراكك وبإمكانك الآن الاستمتاع بجميع الميزات المتقدمة."
                ) if lang == 'ar' else (
                    "✅ Payment confirmed successfully!\n\n"
                    "Your subscription has been activated and you can now enjoy all advanced features."
                )

                await wait_message.edit_text(
                    success_message,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton(
                            get_text('back_to_main', lang),
                            callback_data='back_to_main'
                        )]
                    ])
                )

                # تحديث القائمة الرئيسية بعد ثانيتين
                await asyncio.sleep(2)
                await show_main_menu(update, context, new_message=True)
                return
        else:
            # تحديث رسالة الانتظار - المعاملة معلقة وسيتم التحقق منها تلقائيًا
            error_message = (
                "⚠️ لم يتم العثور على دفع مؤكد. يرجى التأكد من إتمام عملية الدفع والمحاولة مرة أخرى.\n\n"
                "ملاحظات:\n"
                "• قد يستغرق تأكيد الدفع بضع دقائق\n"
                "• تأكد من إكمال عملية الدفع بالكامل\n"
                "• إذا كنت قد أكملت الدفع، يرجى الانتظار قليلاً ثم المحاولة مرة أخرى"
            ) if lang == 'ar' else (
                "⚠️ No confirmed payment found. Please make sure you've completed the payment process and try again.\n\n"
                "Notes:\n"
                "• Payment confirmation may take a few minutes\n"
                "• Make sure you've completed the entire payment process\n"
                "• If you've completed payment, please wait a moment and try again"
            )

            keyboard = [
                [InlineKeyboardButton(
                    "🔍 التحقق مرة أخرى" if lang == 'ar' else "🔍 Verify Again",
                    callback_data='verify_payment' if not transaction_id else f'verify_payment_{transaction_id}'
                )],
                [InlineKeyboardButton(
                    "💳 الدفع عبر PayPal" if lang == 'ar' else "💳 Pay with PayPal",
                    callback_data='payment_paypal'
                )],
                [InlineKeyboardButton(
                    get_text('back_to_main', lang),
                    callback_data='back_to_main'
                )]
            ]

            await wait_message.edit_text(
                error_message,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

    except Exception as e:
        logger.error(f"خطأ في التحقق من الدفع: {str(e)}")
        error_message = (
            "❌ حدث خطأ أثناء التحقق من الدفع. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
            "❌ An error occurred while verifying payment. Please try again"
        )
        if 'wait_message' in locals():
            await wait_message.edit_text(
                error_message,
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton(
                        get_text('back_to_main', lang),
                        callback_data='back_to_main'
                    )]
                ])
            )
        else:
            await query.answer(error_message, show_alert=True)

async def verify_paypal_transaction(user_id: str, amount: float = 5.0, transaction_id: str = None) -> bool:
    """التحقق من معاملة PayPal"""
    try:
        # التحقق من وضع التطوير من قاعدة البيانات
        is_dev_mode = system_settings.get("DEV_MODE", False)

        # إذا لم تكن الإعدادات موجودة في قاعدة البيانات، نستخدم المتغيرات البيئية
        if is_dev_mode is None:
            is_dev_mode = SystemConfig.get_env_var("DEV_MODE", "false").lower() == "true"
            system_settings.set("DEV_MODE", is_dev_mode)

        # في وضع التطوير، نقوم بمحاكاة نجاح العملية
        if is_dev_mode:
            logger.info(f"🔧 وضع التطوير: محاكاة نجاح معاملة PayPal للمستخدم {user_id}")
            return True

        # إذا تم تمرير معرف المعاملة، نتحقق من وجودها في قاعدة البيانات أولاً
        if transaction_id:
            transaction_ref = db.collection('transactions').document(transaction_id)
            transaction_data = transaction_ref.get()

            if transaction_data.exists:
                transaction = transaction_data.to_dict()

                # التحقق من أن المعاملة تخص المستخدم المحدد
                if transaction.get('user_id') == user_id and transaction.get('status') == 'completed':
                    logger.info(f"✅ تم التحقق من معاملة PayPal {transaction_id} للمستخدم {user_id} من قاعدة البيانات")
                    return True

        # الحصول على بيانات الاعتماد من قاعدة البيانات
        paypal_client_id = system_settings.get("PAYPAL_CLIENT_ID", sensitive=True)
        paypal_secret = system_settings.get("PAYPAL_CLIENT_SECRET", sensitive=True)
        is_sandbox = system_settings.get("PAYPAL_SANDBOX_MODE", False)  # تغيير القيمة الافتراضية إلى False (وضع الإنتاج)

        # إذا لم تكن الإعدادات موجودة في قاعدة البيانات، نستخدم المتغيرات البيئية
        if not paypal_client_id:
            paypal_client_id = SystemConfig.get_env_var("PAYPAL_CLIENT_ID")
            system_settings.set("PAYPAL_CLIENT_ID", paypal_client_id, sensitive=True)

        if not paypal_secret:
            paypal_secret = SystemConfig.get_env_var("PAYPAL_CLIENT_SECRET")
            system_settings.set("PAYPAL_CLIENT_SECRET", paypal_secret, sensitive=True)

        if is_sandbox is None:
            is_sandbox = SystemConfig.get_env_var("PAYPAL_SANDBOX_MODE", "false").lower() == "true"  # تغيير القيمة الافتراضية إلى false
            system_settings.set("PAYPAL_SANDBOX_MODE", is_sandbox)

        # إنشاء مدير PayPal
        from integrations.paypal_manager import PayPalManager
        paypal_manager = PayPalManager(
            paypal_client_id,
            paypal_secret,
            is_sandbox
        )

        # الحصول على رمز الوصول
        auth = aiohttp.BasicAuth(paypal_client_id, paypal_secret)
        base_url = "https://api-m.sandbox.paypal.com" if is_sandbox else "https://api-m.paypal.com"

        # الحصول على رمز الوصول
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f'{base_url}/v1/oauth2/token',
                auth=auth,
                data={'grant_type': 'client_credentials'},
                timeout=30
            ) as response:
                if response.status != 200:
                    logger.error(f"خطأ في الحصول على رمز الوصول: {await response.text()}")
                    return False

                token_data = await response.json()
                access_token = token_data['access_token']

        # تعديل نطاق البحث عن المعاملات (آخر 24 ساعة)
        end_time = datetime.now(pytz.UTC)
        start_time = end_time - timedelta(days=1)

        # تنسيق التواريخ بشكل صحيح
        start_date = start_time.strftime('%Y-%m-%dT%H:%M:%S-0000')
        end_date = end_time.strftime('%Y-%m-%dT%H:%M:%S-0000')

        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        # البحث عن المعاملات
        async with aiohttp.ClientSession() as session:
            params = {
                'start_date': start_date,
                'end_date': end_date,
                'fields': 'all',
                'page_size': 100,
                'page': 1
            }

            logger.info(f"جاري البحث عن المعاملات من {start_date} إلى {end_date}")

            async with session.get(
                f'{base_url}/v1/reporting/transactions',
                headers=headers,
                params=params,
                timeout=30
            ) as response:
                if response.status != 200:
                    logger.error(f"خطأ في البحث عن المعاملات: {await response.text()}")
                    return False

                transactions = await response.json()

                # البحث عن المعاملة المطلوبة
                for transaction in transactions.get('transaction_details', []):
                    transaction_info = transaction.get('transaction_info', {})
                    payer_info = transaction.get('payer_info', {})

                    # محاولة العثور على معرف المستخدم في أماكن مختلفة
                    note_user_id = payer_info.get('note', '').strip()
                    custom_field = transaction_info.get('custom_field', '').strip()
                    transaction_note = transaction_info.get('transaction_note', '').strip()

                    found_user_id = note_user_id or custom_field or transaction_note

                    # التحقق من المعاملة
                    if (
                        transaction_info.get('transaction_status') == 'S' and  # ناجحة
                        abs(float(transaction_info.get('transaction_amount', {}).get('value', 0)) - amount) < 0.01 and  # المبلغ صحيح
                        found_user_id == user_id
                    ):
                        # حفظ المعاملة في قاعدة البيانات إذا لم يكن لدينا معرف معاملة
                        if not transaction_id:
                            new_transaction_id = transaction_info.get('transaction_id', '')
                            if new_transaction_id:
                                transaction_ref = db.collection('transactions').document(new_transaction_id)
                                transaction_data = {
                                    'user_id': user_id,
                                    'amount': amount,
                                    'created_at': datetime.now().isoformat(),
                                    'status': 'completed',
                                    'completed_at': datetime.now().isoformat(),
                                    'used': False,
                                    'payment_method': 'paypal',
                                    'paypal_transaction_id': new_transaction_id,
                                    'verification_method': 'manual'
                                }
                                transaction_ref.set(transaction_data)
                                transaction_id = new_transaction_id
                        elif transaction_id:
                            # تحديث المعاملة الموجودة
                            transaction_ref = db.collection('transactions').document(transaction_id)
                            transaction_ref.update({
                                'status': 'completed',
                                'completed_at': datetime.now().isoformat(),
                                'paypal_transaction_id': transaction_info.get('transaction_id', ''),
                                'verification_method': 'manual'
                            })

                        logger.info(f"✅ تم العثور على معاملة PayPal صالحة للمستخدم {user_id}")
                        return True

        # إذا لم يتم العثور على معاملة، نتحقق من وجود معاملات معلقة للمستخدم
        if transaction_id:
            # تحديث حالة المعاملة للتحقق منها مرة أخرى لاحقًا
            transaction_ref = db.collection('transactions').document(transaction_id)
            transaction_data = transaction_ref.get()

            if transaction_data.exists:
                transaction = transaction_data.to_dict()
                verification_attempts = transaction.get('verification_attempts', 0) + 1

                transaction_ref.update({
                    'verification_attempts': verification_attempts,
                    'last_verification': datetime.now().isoformat()
                })

                # إذا كان عدد المحاولات أقل من الحد الأقصى، نضيف المعاملة للتحقق التلقائي
                if verification_attempts < 12:  # 12 محاولة (ساعة واحدة)
                    logger.info(f"⏳ سيتم التحقق من المعاملة {transaction_id} للمستخدم {user_id} تلقائيًا لاحقًا")
                    # سيتم التحقق منها تلقائيًا لاحقًا
                    return False
                else:
                    # تحديث حالة المعاملة إلى فاشلة
                    transaction_ref.update({
                        'status': 'failed',
                        'failure_reason': 'تجاوز الحد الأقصى لمحاولات التحقق'
                    })
                    logger.warning(f"❌ تم تجاوز الحد الأقصى لمحاولات التحقق للمعاملة {transaction_id}")
                    return False

        logger.warning(f"❌ لم يتم العثور على معاملة PayPal صالحة للمستخدم {user_id}")
        return False

    except aiohttp.ClientError as e:
        logger.error(f"خطأ في الاتصال مع PayPal: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"خطأ غير متوقع في التحقق من معاملة PayPal: {str(e)}")
        return False

async def activate_subscription(user_id: str, transaction_id: str) -> bool:
    """تفعيل الاشتراك بعد التحقق من الدفع (واجهة متوافقة مع الكود القديم)"""
    try:
        # استخدام الدالة الموحدة في نظام الاشتراك
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')
        return await subscription_system.activate_subscription(user_id, transaction_id, lang)
    except Exception as e:
        logger.error(f"Error activating subscription for user {user_id}: {str(e)}")
        return False

async def setup_price_alert(update: Update, context: CallbackContext, symbol: str):
    """إعداد تنبيه سعري"""
    try:
        user_id = str(update.effective_user.id)
        lang = subscription_system.get_user_settings(user_id).get('lang', 'ar')

        # التحقق من حدود التنبيهات المجانية
        if not subscription_system.is_subscribed(user_id):
            free_usage = subscription_system.get_free_usage(user_id)
            if free_usage['alerts'] <= 0:
                await update.callback_query.answer(
                    "عذراً، لقد استنفدت عدد التنبيهات المجانية. قم بالترقية للحصول على تنبيهات غير محدودة!" if lang == 'ar' else
                    "Sorry, you've used all free alerts. Upgrade to get unlimited alerts!",
                    show_alert=True
                )
                return

        # الحصول على السعر الحالي
        market_data = await ca.get_market_data(symbol)
        if not market_data:
            await update.callback_query.answer(
                "عذراً، لم نتمكن من الحصول على سعر العملة" if lang == 'ar' else
                "Sorry, couldn't get current price",
                show_alert=True
            )
            return

        current_price = market_data['price']

        # إنشاء أزرار التنبيه
        keyboard = [
            [
                InlineKeyboardButton(
                    f"↗️ +1% ({current_price * 1.01:.4f})",
                    callback_data=f'alert_above_{symbol}_{current_price * 1.01}'
                ),
                InlineKeyboardButton(
                    f"↗️ +2% ({current_price * 1.02:.4f})",
                    callback_data=f'alert_above_{symbol}_{current_price * 1.02}'
                )
            ],
            [
                InlineKeyboardButton(
                    f"↘️ -1% ({current_price * 0.99:.4f})",
                    callback_data=f'alert_below_{symbol}_{current_price * 0.99}'
                ),
                InlineKeyboardButton(
                    f"↘️ -2% ({current_price * 0.98:.4f})",
                    callback_data=f'alert_below_{symbol}_{current_price * 0.98}'
                )
            ],
            [
                InlineKeyboardButton(
                    "🔙 Back" if lang == 'en' else "🔙 رجوع",
                    callback_data='back_to_main'
                )
            ]
        ]

        # إعداد نص الرسالة
        header_text = "Choose alert price or enter custom value:" if lang == 'en' else "اختر نسبة التنبيه أو ادخل سعراً مخصصاً:"
        price_text = "Current price" if lang == 'en' else "السعر الحالي"
        alert_text = f"💱 {symbol}\n💰 {price_text}: {current_price:.4f}\n\n{header_text}"

        # إضافة ملاحظة للمستخدمين غير المشتركين
        if not subscription_system.is_subscribed(user_id):
            note_text = "⭐️ Note: Custom alert is available for subscribers only" if lang == 'en' else "⭐️ ملاحظة: التنبيه المخصص متاح فقط للمشتركين"
            alert_text += f"\n\n{note_text}"

        try:
            await update.callback_query.edit_message_text(
                text=alert_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except Exception as edit_error:
            await update.callback_query.message.reply_text(
                text=alert_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

    except Exception as e:
        logger.error(f"Error setting up price alert: {str(e)}")
        error_message = "Sorry, an error occurred. Please try again" if lang == 'en' else "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى"
        await update.callback_query.answer(error_message, show_alert=True)
        await show_main_menu(update, context, new_message=True)

async def handle_custom_alert(update: Update, context: CallbackContext, symbol: str):
    """معالجة التنبيه المخصص"""
    try:
        user_id = str(update.effective_user.id)
        lang = subscription_system.get_user_settings(user_id).get('lang', 'ar')

        # التحقق من الاشتراك
        if not subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "عذراً، التنبيه المخصص متاح فقط للمشتركين" if lang == 'ar' else
                "Sorry, custom alerts are only available for subscribers",
                show_alert=True
            )
            return

        # الحصول على السعر الحالي
        market_data = await ca.get_market_data(symbol)
        if not market_data:
            await update.callback_query.answer(
                "عذراً، لم نتمكن من الحصول على سعر العملة" if lang == 'ar' else
                "Sorry, couldn't get current price",
                show_alert=True
            )
            return

        current_price = market_data['price']

        # تخزين حالة المستخدم
        user_states[user_id] = {
            'state': 'waiting_for_custom_alert',
            'symbol': symbol,
            'current_price': current_price
        }

        # إرسال رسالة للمستخدم
        keyboard = [[
            InlineKeyboardButton(
                "🔙 إلغاء" if lang == 'ar' else "🔙 Cancel",
                callback_data='back_to_main'
            )
        ]]

        hint_text = get_text('alert_price_hint', lang)
        message_text = (
            f"💱 {symbol}\n"
            f"💰 {get_text('current_price', lang)}: {current_price:.4f}\n\n"
            f"{hint_text}"
        )

        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        logger.error(f"Error handling custom alert: {str(e)}")
        await update.callback_query.answer(
            "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
            "Sorry, an error occurred. Please try again",
            show_alert=True
        )

async def process_custom_alert(update: Update, context: CallbackContext):
    """معالجة إدخال السعر المخصص"""
    try:
        user_id = str(update.effective_user.id)
        lang = subscription_system.get_user_settings(user_id).get('lang', 'ar')

        if user_id not in user_states or user_states[user_id].get('state') != 'waiting_for_custom_alert':
            return

        state_data = user_states[user_id]
        symbol = state_data['symbol']
        current_price = state_data['current_price']
        price_input = update.message.text.strip()

        # معالجة النسبة المئوية
        if price_input.endswith('%'):
            try:
                percentage = float(price_input.rstrip('%').strip())
                target_price = current_price * (1 + percentage/100)
                condition = 'above' if percentage > 0 else 'below'
            except ValueError:
                await update.message.reply_text(
                    "عذراً، الرجاء إدخال نسبة صحيحة" if lang == 'ar' else
                    "Sorry, please enter a valid percentage"
                )
                return
        else:
            try:
                target_price = float(price_input)
                condition = 'above' if target_price > current_price else 'below'
            except ValueError:
                await update.message.reply_text(
                    "عذراً، الرجاء إدخال سعر صحيح" if lang == 'ar' else
                    "Sorry, please enter a valid price"
                )
                return

        # إضافة التنبيه
        alerts_ref = db.collection('alerts').document(user_id)
        alert_data = alerts_ref.get()

        if alert_data.exists:
            user_alerts = alert_data.to_dict()
        else:
            user_alerts = {}

        if symbol not in user_alerts:
            user_alerts[symbol] = []

        user_alerts[symbol].append({
            'price': target_price,
            'condition': condition,
            'created_at': datetime.now().isoformat()
        })

        alerts_ref.set(user_alerts)

        # إرسال تأكيد
        condition_text = get_text('alert_condition_above', lang) if condition == 'above' else get_text('alert_condition_below', lang)
        await update.message.reply_text(
            get_text('alert_added', lang).format(
                symbol=symbol,
                condition=condition_text,
                price=target_price
            )
        )

        # مسح حالة المستخدم
        del user_states[user_id]

        # العودة للقائمة الرئيسية
        await show_main_menu(update, context, new_message=True)

    except Exception as e:
        logger.error(f"Error processing custom alert: {str(e)}")
        await update.message.reply_text(
            "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
            "Sorry, an error occurred. Please try again"
        )

# تم إزالة دالة إعداد التقارير الدورية

async def customize_indicators(update: Update, context: CallbackContext, symbol: str):
    """تخصيص المؤشرات الفنية"""
    try:
        user_id = str(update.effective_user.id)
        lang = subscription_system.get_user_settings(user_id).get('lang', 'ar')

        # التحقق من الاشتراك
        if not subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "عذراً، هذه الميزة متوفرة فقط للمشتركين" if lang == 'ar' else
                "Sorry, this feature is only available for subscribers",
                show_alert=True
            )
            return

        # إنشاء أزرار المؤشرات
        keyboard = []

        # إضافة زر الرجوع والتحديث
        keyboard.append([
            InlineKeyboardButton(
                "🔄 تحديث" if lang == 'ar' else "🔄 Refresh",
                callback_data=f'refresh_analysis_{symbol}'
            ),
            InlineKeyboardButton(
                "🔙 رجوع" if lang == 'ar' else "🔙 Back",
                callback_data='back_to_main'
            )
        ])

        # عنوان القائمة
        title = "📊 تخصيص المؤشرات الفنية" if lang == 'ar' else "📊 Customize Technical Indicators"
        message_text = title

        try:
            # تحديث الرسالة الحالية
            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )
        except Exception as edit_error:
            logger.error(f"Error updating indicators message: {str(edit_error)}")
            # إذا فشل التحديث، نرسل رسالة جديدة
            await update.callback_query.message.reply_text(
                text=message_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

    except Exception as e:
        logger.error(f"Error customizing indicators: {str(e)}")
        error_message = "❌ حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else "❌ An error occurred. Please try again"
        await update.callback_query.answer(text=error_message, show_alert=True)

        # إضافة زر الرجوع والتحديث
        keyboard.append([
            InlineKeyboardButton(
                "🔄 تحديث" if lang == 'ar' else "🔄 Refresh",
                callback_data=f'refresh_analysis_{symbol}'
            ),
            InlineKeyboardButton(
                "🔙 رجوع" if lang == 'ar' else "🔙 Back",
                callback_data='back_to_main'
            )
        ])

        # عنوان القائمة
        title = "📊 تخصيص المؤشرات الفنية" if lang == 'ar' else "📊 Customize Technical Indicators"
        description = (
            "\n\nاختر المؤشرات التي تريد إضافتها أو إزالتها:" if lang == 'ar' else
            "\n\nSelect indicators to add or remove:"
        )
        message_text = f"{title}{description}"

        try:
            # تحديث الرسالة الحالية
            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )
        except Exception as edit_error:
            logger.error(f"Error updating indicators message: {str(edit_error)}")
            # إذا فشل التحديث، نرسل رسالة جديدة
            await update.callback_query.message.reply_text(
                text=message_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

    except Exception as e:
        logger.error(f"Error customizing indicators: {str(e)}")
        error_message = "❌ حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else "❌ An error occurred. Please try again"
        await update.callback_query.answer(text=error_message, show_alert=True)

async def refresh_analysis(update: Update, context: CallbackContext, symbol: str):
    """تحديث التحليل"""
    try:
        # تحديث التحليل
        await analyze_symbol(update, context, symbol, message=update.callback_query.message)
    except Exception as e:
        logger.error(f"Error refreshing analysis: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ. الرجاء المحاولة مرة أخرى")

class GitHubBackup:
    """فئة لإدارة النسخ الاحتياطي على GitHub"""
    def __init__(self):
        self.token = GITHUB_TOKEN
        self.repo = GITHUB_REPO
        self.owner = GITHUB_OWNER
        self.headers = {
            'Authorization': f'token {self.token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        self.base_url = 'https://api.github.com'
        self.last_backup_hash = None

    async def ensure_backup_folder_exists(self) -> bool:
        """التحقق من وجود مجلد النسخ الاحتياطي وإنشائه إذا لم يكن موجوداً"""
        try:
            # التحقق من وجود المجلد
            url = f'{self.base_url}/repos/{self.owner}/{self.repo}/contents/backups'

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 404:
                        # المجلد غير موجود، نقوم بإنشائه
                        create_url = f'{self.base_url}/repos/{self.owner}/{self.repo}/contents/backups/.gitkeep'
                        payload = {
                            'message': 'Create backups folder',
                            'content': base64.b64encode(b'').decode(),  # ملف فارغ
                            'branch': 'main'
                        }

                        async with session.put(create_url, headers=self.headers, json=payload) as create_response:
                            if create_response.status == 201:
                                logger.info("✅ تم إنشاء مجلد النسخ الاحتياطي بنجاح")
                                return True
                            else:
                                logger.error(f"❌ فشل في إنشاء مجلد النسخ الاحتياطي: {await create_response.text()}")
                                return False
                    elif response.status == 200:
                        # المجلد موجود بالفعل
                        return True
                    else:
                        logger.error(f"❌ خطأ في التحقق من وجود مجلد النسخ الاحتياطي: {await response.text()}")
                        return False

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من مجلد النسخ الاحتياطي: {str(e)}")
            return False

    async def upload_to_github(self, data: dict) -> bool:
        """رفع النسخة الاحتياطية إلى GitHub"""
        try:
            # التأكد من وجود مجلد النسخ الاحتياطي
            if not await self.ensure_backup_folder_exists():
                logger.error("❌ فشل في التحقق من/إنشاء مجلد النسخ الاحتياطي")
                return False

            # تشفير البيانات
            key = Fernet.generate_key()
            f = Fernet(key)
            encrypted_data = f.encrypt(json.dumps(data).encode())

            # إنشاء اسم الملف
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'backup_{current_time}.enc'

            # رفع الملف
            path = 'backups'
            url = f'{self.base_url}/repos/{self.owner}/{self.repo}/contents/{path}/{filename}'

            content = base64.b64encode(encrypted_data).decode()
            payload = {
                'message': f'Automated backup - {current_time}',
                'content': content,
                'branch': 'main'
            }

            async with aiohttp.ClientSession() as session:
                async with session.put(url, headers=self.headers, json=payload) as response:
                    if response.status == 201:
                        logger.info(f"✅ تم رفع النسخة الاحتياطية بنجاح: {filename}")

                        # حفظ مفتاح التشفير في Firestore
                        keys_ref = db.collection('backup_keys').document(filename)
                        keys_ref.set({
                            'key': key.decode(),
                            'created_at': datetime.now().isoformat()
                        })

                        return True
                    else:
                        logger.error(f"❌ خطأ في رفع الملف: {await response.text()}")
                        return False

        except Exception as e:
            logger.error(f"❌ خطأ في رفع النسخة الاحتياطية: {str(e)}")
            return False

    async def cleanup_old_backups(self):
        """حذف النسخ الاحتياطية القديمة (أكثر من أسبوع)"""
        try:
            # جلب قائمة الملفات
            path = 'backups'
            url = f'{self.base_url}/repos/{self.owner}/{self.repo}/contents/{path}'

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        files = await response.json()
                        week_ago = datetime.now() - timedelta(days=7)

                        for file in files:
                            if file['type'] == 'file' and file['name'].startswith('backup_'):
                                # استخراج التاريخ من اسم الملف
                                try:
                                    file_date = datetime.strptime(
                                        file['name'].split('_')[1].split('.')[0],
                                        "%Y%m%d%H%M%S"
                                    )
                                    if file_date < week_ago:
                                        # حذف الملف
                                        delete_url = f'{self.base_url}/repos/{self.owner}/{self.repo}/contents/{file["path"]}'
                                        payload = {
                                            'message': f'Cleanup old backup - {file["name"]}',
                                            'sha': file['sha'],
                                            'branch': 'main'
                                        }

                                        async with session.delete(delete_url, headers=self.headers, json=payload) as del_response:
                                            if del_response.status == 200:
                                                logger.info(f"تم حذف النسخة الاحتياطية القديمة: {file['name']}")

                                                # حذف مفتاح التشفير
                                                keys_ref = db.collection('backup_keys').document(file['name'])
                                                keys_ref.delete()
                                            else:
                                                logger.error(f"خطأ في حذف الملف: {await del_response.text()}")

                                except ValueError:
                                    continue

        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")

# تهيئة نظام النسخ الاحتياطي
github_backup = GitHubBackup()

async def perform_backup(update: Update = None, context: CallbackContext = None):
    """تنفيذ النسخ الاحتياطي إذا كانت هناك تغييرات"""
    try:
        # التحقق من أن المستخدم هو المطور إذا تم استدعاء الدالة من أمر
        if update and str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
            await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
            logger.warning(f"محاولة غير مصرح بها لإنشاء نسخة احتياطية من قبل المستخدم {update.effective_user.id}")
            return

        # التحقق من وجود تغييرات
        if await github_backup.check_changes():
            # إنشاء النسخة الاحتياطية
            backup_data = await github_backup.create_backup()
            if backup_data:
                # رفع النسخة الاحتياطية
                if await github_backup.upload_to_github(backup_data):
                    # تنظيف النسخ القديمة
                    await github_backup.cleanup_old_backups()

                    # إرسال تأكيد للمطور
                    success_message = "✅ تم إنشاء نسخة احتياطية جديدة بنجاح"

                    if context and context.bot:
                        await context.bot.send_message(
                            chat_id=SystemConfig.DEVELOPER_ID,
                            text=success_message
                        )

                    if update:
                        await update.message.reply_text(success_message)

                    logger.info("تم إنشاء نسخة احتياطية جديدة بنجاح")
                    return True
        else:
            # لا توجد تغييرات تتطلب نسخة احتياطية جديدة
            if update:
                await update.message.reply_text("ℹ️ لا توجد تغييرات تتطلب نسخة احتياطية جديدة")

        return False

    except Exception as e:
        error_message = f"⚠️ خطأ في النسخ الاحتياطي: {str(e)}"
        logger.error(f"خطأ في تنفيذ النسخ الاحتياطي: {str(e)}")

        if context and context.bot:
            await context.bot.send_message(
                chat_id=SystemConfig.DEVELOPER_ID,
                text=error_message
            )

        if update:
            await update.message.reply_text(error_message)

        return False

async def stop_all_scheduled_tasks(update: Update, context: CallbackContext):
    """إيقاف وإزالة جميع المهام المجدولة (للمطور فقط)"""
    try:
        # التحقق من أن المستخدم هو المطور
        if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
            await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
            logger.warning(f"محاولة غير مصرح بها لإيقاف المهام من قبل المستخدم {update.effective_user.id}")
            return

        # إيقاف جميع المهام
        job_count = 0
        for job in context.job_queue.jobs():
            job.schedule_removal()
            job_count += 1
        await context.job_queue.stop()

        # تنظيف جميع المجموعات المرتبطة بالمهام
        # حذف التنبيهات النشطة
        alerts_ref = db.collection('alerts')
        alerts = alerts_ref.stream()
        alert_count = 0
        for alert in alerts:
            if alert.id != '_metadata' and alert.id != '_init':
                alert.reference.delete()
                alert_count += 1

        # تنظيف البيانات المؤقتة منتهية الصلاحية
        cache_count = firestore_cache.clear_expired()

        # إرسال تقرير مفصل
        report = f"""✅ تم إيقاف وإزالة جميع المهام المجدولة بنجاح

📊 تقرير العملية:
• المهام الموقوفة: {job_count}
• التنبيهات المحذوفة: {alert_count}
• البيانات المؤقتة المنظفة: {cache_count}

⚠️ ملاحظة: ستحتاج إلى إعادة تشغيل البوت لاستئناف المهام المجدولة."""

        logger.info(f"تم إيقاف {job_count} مهمة وحذف {alert_count} تنبيه وتنظيف {cache_count} بيانات مؤقتة")
        await update.message.reply_text(report)

    except Exception as e:
        error_msg = str(e)
        logger.error(f"❌ خطأ في إيقاف المهام المجدولة: {error_msg}", exc_info=False)
        await update.message.reply_text("❌ حدث خطأ أثناء إيقاف المهام المجدولة")

async def show_upgrade_info(update: Update, context: CallbackContext):
    """عرض معلومات الترقية"""
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # التحقق من حالة الاشتراك
        if subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                get_text('already_subscribed', lang),
                show_alert=True
            )
            return

        # عرض معلومات الترقية

        # إنشاء نص الترقية
        message_text = f"""{'🔍 Bot Usage Guide:' if lang == 'en' else '🔍 دليل استخدام البوت:'}

1️⃣ {'Free Version:' if lang == 'en' else 'النسخة المجانية:'}
• {'3 cryptocurrency analyses per day' if lang == 'en' else '3 تحليلات يومياً للعملات الرقمية'}
• {'RSI and EMA indicators only' if lang == 'en' else 'مؤشر RSI و EMA فقط'}
• {'Hourly updates' if lang == 'en' else 'تحديث كل ساعة'}
• {'One active alert' if lang == 'en' else 'تنبيه واحد نشط'}

2️⃣ {'Premium Features (5 USD/week):' if lang == 'en' else 'مميزات النسخة المدفوعة (5 USD أسبوعياً):'}
• {'Unlimited cryptocurrency analysis' if lang == 'en' else 'تحليل غير محدود للعملات الرقمية'}
• {'All advanced indicators:' if lang == 'en' else 'جميع المؤشرات المتقدمة:'}
  - {'Relative Strength Index (RSI)' if lang == 'en' else 'مؤشر القوة النسبية (RSI)'}
  - {'Moving Average Convergence Divergence (MACD)' if lang == 'en' else 'الماكد (MACD)'}
  - {'Bollinger Bands (BB)' if lang == 'en' else 'مؤشر البولنجر (BB)'}
  - {'Exponential Moving Average (EMA)' if lang == 'en' else 'المتوسط المتحرك الأسي (EMA)'}
  - {'Stochastic RSI' if lang == 'en' else 'مؤشر القوة النسبية الاستوكاستك'}
  - {'Average Directional Index (ADX)' if lang == 'en' else 'مؤشر الاتجاه (ADX)'}
• {'Instant updates' if lang == 'en' else 'تحديث فوري للتحليلات'}
• {'Unlimited alerts' if lang == 'en' else 'تنبيهات غير محدودة'}
• {'Advanced portfolio management' if lang == 'en' else 'إدارة محفظة متقدمة'}
• {'Automated trading strategies' if lang == 'en' else 'استراتيجيات تداول آلية'}
• {'Price predictions' if lang == 'en' else 'تنبؤات سعرية'}
• {'Multi-timeframe analysis' if lang == 'en' else 'تحليل متعدد الإطارات الزمنية'}

3️⃣ {'Payment Method:' if lang == 'en' else 'طريقة الدفع:'}
• PayPal
• {'Amount: 5 USD' if lang == 'en' else 'المبلغ: 5 USD'}

4️⃣ {'Subscription Steps:' if lang == 'en' else 'خطوات الاشتراك:'}
1. {'Click "✨ Upgrade Account"' if lang == 'en' else 'اضغط على "✨ ترقية الحساب"'}
2. {'Click PayPal payment button' if lang == 'en' else 'اضغط على زر الدفع عبر PayPal'}
3. {'Complete payment process' if lang == 'en' else 'أكمل عملية الدفع'}
4. {'Click "Verify Payment"' if lang == 'en' else 'اضغط على "تحقق من الدفع"'}

⚠️ {'Important Notes:' if lang == 'en' else 'ملاحظات مهمة:'}
• {'Wait for transaction completion (2-5 mins)' if lang == 'en' else 'انتظر اكتمال المعاملة قبل التحقق'}
• {'Subscription is non-refundable' if lang == 'en' else 'الاشتراك غير قابل للاسترداد'}"""

        keyboard = [
            [InlineKeyboardButton(
                "💳 Pay with PayPal" if lang == 'en' else "💳 الدفع عبر PayPal",
                callback_data='payment_paypal'
            )],
            [InlineKeyboardButton(
                "✅ Verify Payment" if lang == 'en' else "✅ تحقق من الدفع",
                callback_data='verify_payment'
            )],
            [InlineKeyboardButton(
                "🔙 Back" if lang == 'en' else "🔙 رجوع",
                callback_data='back_to_main'
            )]
        ]

        await update.callback_query.message.edit_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"Error showing upgrade info: {str(e)}")
        await update.callback_query.answer(
            get_text('error_occurred', lang),
            show_alert=True
        )

async def api_setup_command(update: Update, context: CallbackContext, preselect_platform=None):
    """إعداد مفاتيح API"""
    try:
        await setup_api_keys(update, context, api_manager, subscription_system)

        # إذا تم تحديد منصة مسبقًا، توجيه المستخدم إلى إعداد تلك المنصة
        if preselect_platform:
            user_id = str(update.effective_user.id)
            settings = subscription_system.get_user_settings(user_id)
            lang = settings.get('lang', 'ar')
            await show_api_instructions(update, context, preselect_platform, lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': f'{preselect_platform}_key'}
    except Exception as e:
        logger.error(f"خطأ في إعداد مفاتيح API: {str(e)}")
        await update.message.reply_text("⚠️ حدث خطأ أثناء إعداد مفاتيح API. الرجاء المحاولة مرة أخرى.")

async def api_info_command(update: Update, context: CallbackContext):
    """عرض معلومات API"""
    try:
        await show_api_info(update, context, api_manager, subscription_system)
    except Exception as e:
        logger.error(f"خطأ في عرض معلومات API: {str(e)}")
        await update.message.reply_text("⚠️ حدث خطأ أثناء عرض معلومات API. الرجاء المحاولة مرة أخرى.")

async def delete_api_command(update: Update, context: CallbackContext):
    """حذف مفاتيح API"""
    try:
        await delete_api_keys_ui(update, context, api_manager, subscription_system)
    except Exception as e:
        logger.error(f"خطأ في حذف مفاتيح API: {str(e)}")
        await update.message.reply_text("⚠️ حدث خطأ أثناء حذف مفاتيح API. الرجاء المحاولة مرة أخرى.")

async def show_user_stats(update: Update, context: CallbackContext):
    """عرض إحصائيات المستخدمين للبوت"""
    try:
        # سجلات تصحيح للتحقق من قيم المتغيرات
        user_id = str(update.effective_user.id)
        developer_id = SystemConfig.DEVELOPER_ID
        logger.info(f"معرف المستخدم: '{user_id}' (النوع: {type(user_id).__name__})")
        logger.info(f"معرف المطور: '{developer_id}' (النوع: {type(developer_id).__name__})")
        logger.info(f"هل المعرفان متساويان؟ {user_id == developer_id}")
        
        # التحقق من أن المستخدم هو المطور
        if user_id != developer_id:
            await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
            logger.warning(f"محاولة غير مصرح بها لعرض إحصائيات المستخدمين من قبل المستخدم {update.effective_user.id}")
            return

        # جلب إحصائيات المستخدمين من جدول users
        users_ref = db.collection('users')
        total_users = 0
        active_subs = 0

        for doc in users_ref.get():
            if doc.id.startswith('_'):  # تجاهل الوثائق الوصفية
                continue
            total_users += 1
            user_data = doc.to_dict()
            if user_data.get('subscriptionStatus') == 'مشترك':
                try:
                    expiry = datetime.fromisoformat(user_data.get('subscriptionExpiry'))
                    if expiry > datetime.now():
                        active_subs += 1
                except (KeyError, ValueError):
                    logger.warning(f"تحذير: مفتاح 'subscriptionExpiry' غير موجود أو تنسيقه غير صحيح للمستخدم {doc.id}")

        inactive_subs = total_users - active_subs

        # إنشاء النص للإحصائيات
        stats_text = (
            f"📊 *إحصائيات المستخدمين*\n\n"
            f"• إجمالي المستخدمين: {total_users}\n"
            f"• المشتركين: {active_subs}\n"
            f"• غير المشتركين: {inactive_subs}"
        )

        await update.message.reply_text(stats_text, parse_mode=ParseMode.MARKDOWN)

    except Exception as e:
        logger.error(f"خطأ في عرض إحصائيات المستخدمين: {str(e)}")
        await update.message.reply_text("❌ حدث خطأ أثناء عرض إحصائيات المستخدمين")

async def add_user_to_users_collection(user_id: str, username: str):
    """إضافة المستخدم إلى جدول users في Firestore"""
    try:
        users_ref = db.collection('users')
        user_doc = users_ref.document(user_id)

        if not user_doc.get().exists:
            user_data = {
                'userId': user_id,
                'username': username,
                'chatId': user_id,
                'subscriptionStatus': 'غير مشترك',
                'subscriptionExpiry': None,
                'language': 'ar',
                'createdAt': datetime.now().isoformat()
            }
            user_doc.set(user_data)
            logger.info(f"تم إضافة المستخدم {user_id} إلى جدول users بنجاح")

            # منح يوم مجاني للمستخدم الجديد
            if free_day_system.grant_free_day(user_id):
                logger.info(f"تم منح يوم مجاني للمستخدم الجديد {user_id}")
            else:
                logger.warning(f"فشل في منح يوم مجاني للمستخدم الجديد {user_id}")
        else:
            logger.info(f"المستخدم {user_id} موجود بالفعل في جدول users")

    except Exception as e:
        logger.error(f"خطأ في إضافة المستخدم {user_id} إلى جدول users: {str(e)}")

async def check_expired_subscriptions(context: CallbackContext):
    """التحقق من الاشتراكات المنتهية وتحديث حالة المستخدمين"""
    try:
        logger.info(f"⏰ بدء التحقق من الاشتراكات المنتهية في {datetime.now().isoformat()}")
        # الحصول على جميع المستخدمين المشتركين
        users_ref = db.collection('users')
        users = users_ref.where(filter=FieldFilter('subscriptionStatus', '==', 'مشترك')).get()

        expired_count = 0
        for user in users:
            user_data = user.to_dict()
            user_id = user.id

            try:
                if 'subscriptionExpiry' not in user_data:
                    logger.warning(f"تاريخ انتهاء الاشتراك غير موجود للمستخدم {user_id}")
                    continue

                expiry = datetime.fromisoformat(user_data.get('subscriptionExpiry'))

                if expiry <= datetime.now():
                    # تحديث حالة المستخدم إلى غير مشترك
                    await _update_expired_subscription(user_id)
                    expired_count += 1

            except (ValueError, TypeError) as e:
                logger.warning(f"تنسيق تاريخ انتهاء الاشتراك غير صالح للمستخدم {user_id}: {str(e)}")

        logger.info(f"تم التحقق من الاشتراكات المنتهية. تم تحديث {expired_count} اشتراك منتهي.")

    except Exception as e:
        logger.error(f"خطأ في التحقق من الاشتراكات المنتهية: {str(e)}")

async def _update_expired_subscription(user_id: str):
    """تحديث حالة الاشتراك المنتهي"""
    try:
        # تحديث في جدول users
        users_ref = db.collection('users').document(user_id)
        users_ref.update({
            'subscriptionStatus': 'غير مشترك',
            'subscriptionExpiry': None
        })

        # تحديث في جدول subscriptions
        subscription_ref = db.collection('subscriptions').document(user_id)
        subscription_ref.update({
            'status': 'expired',
            'expiry': datetime.now().isoformat()
        })

        # تحديث الذاكرة المحلية
        current_time = datetime.now()
        subscription_system._subscription_cache[user_id] = {
            'is_active': False,
            'expiry': None,
            'features': subscription_system.get_free_features()
        }
        subscription_system._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

        logger.info(f"تم تحديث حالة الاشتراك للمستخدم {user_id} إلى منتهي")

    except Exception as e:
        logger.error(f"خطأ في تحديث حالة الاشتراك المنتهي للمستخدم {user_id}: {str(e)}")

async def notify_expiring_subscriptions(context: CallbackContext):
    """إشعار المستخدمين بقرب انتهاء اشتراكاتهم"""
    try:
        # الحصول على جميع المستخدمين المشتركين
        users_ref = db.collection('users')
        users = users_ref.where(filter=FieldFilter('subscriptionStatus', '==', 'مشترك')).get()

        for user in users:
            user_data = user.to_dict()
            user_id = user.id

            try:
                if 'subscriptionExpiry' not in user_data:
                    continue

                expiry = datetime.fromisoformat(user_data.get('subscriptionExpiry'))
                now = datetime.now()

                # حساب الوقت المتبقي
                time_left = expiry - now
                hours_left = time_left.total_seconds() / 3600

                # إرسال إشعارات مختلفة حسب الوقت المتبقي
                if 0 < hours_left <= 24:  # أقل من 24 ساعة
                    await _send_expiry_notification(context, user_id, 'soon', hours_left)
                elif hours_left <= 0:  # منتهي بالفعل
                    await _update_expired_subscription(user_id)
                    await _send_expiry_notification(context, user_id, 'expired')
                elif hours_left <= 48:  # أقل من 48 ساعة
                    await _send_expiry_notification(context, user_id, 'warning', hours_left)

            except (ValueError, TypeError) as e:
                logger.warning(f"تنسيق تاريخ انتهاء الاشتراك غير صالح للمستخدم {user_id}: {str(e)}")

    except Exception as e:
        logger.error(f"خطأ في إرسال إشعارات الاشتراكات: {str(e)}")

async def _send_expiry_notification(context: ContextTypes.DEFAULT_TYPE, user_id: str, notification_type: str, hours_left: float = 0):
    """إرسال إشعار انتهاء الاشتراك"""
    try:
        # تحديد نوع الإشعار
        if notification_type == 'soon':
            message = f"🔔 تنبيه: اشتراكك سينتهي خلال {int(hours_left)} ساعة. يرجى التجديد للاستمرار في الاستفادة من المزايا."
        elif notification_type == 'warning':
            message = f"⚠️ تحذير: اشتراكك سينتهي خلال {int(hours_left)} ساعة. يرجى التجديد قريباً."
        elif notification_type == 'expired':
            message = "⚠️ انتهى اشتراكك. يرجى التجديد للاستمرار في الاستفادة من المزايا."
        else:
            return

        # إرسال الإشعار
        await context.bot.send_message(
            chat_id=user_id,
            text=message
        )

        logger.info(f"تم إرسال إشعار {notification_type} للمستخدم {user_id}")

    except Exception as e:
        logger.error(f"خطأ في إرسال إشعار انتهاء الاشتراك للمستخدم {user_id}: {str(e)}")

# دالة مستقلة لتشغيل الرابط
async def ping_url(url: str, timeout: int = 10) -> bool:
    """
    تشغيل رابط محدد والتحقق من استجابته

    Args:
        url: الرابط المراد تشغيله
        timeout: مهلة الانتظار بالثواني

    Returns:
        True إذا كان الرابط يعمل، False خلاف ذلك
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=timeout) as response:
                status = response.status
                logger.info(f"🔄 تم تشغيل الرابط: {url} - الاستجابة: {status}")
                return status == 200
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الرابط {url}: {str(e)}")
        return False

# دالة لتشغيل رابط Koyeb (تم تعطيلها)
async def ping_koyeb_app():
    """
    تم تعطيل هذه الدالة لأنها لم تعد ضرورية مع إعدادات Koyeb الجديدة
    """
    logger.info("🔄 تم تعطيل تشغيل رابط Koyeb لأنه لم يعد ضروريًا")
    return True

# إضافة المهمة المجدولة إلى TelegramBot
class TelegramBot:
    """فئة لإدارة البوت وتنظيم دورة حياته"""
    def __init__(self):
        self.application = None
        self.config = Config()
        self._running = False

    async def setup(self):
        """إعداد البوت وتهيئة المكونات"""
        # تعطيل سجلات غير ضرورية
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("apscheduler").setLevel(logging.WARNING)

        # تهيئة النظام
        if not await initialize_system():
            raise Exception("فشل في تهيئة النظام")

        # إنشاء التطبيق مع زيادة مهلة الاتصال
        from telegram.request import HTTPXRequest
        # زيادة مهلة الاتصال إلى 60 ثانية بدلاً من القيمة الافتراضية
        request = HTTPXRequest(connection_pool_size=8, connect_timeout=60.0, read_timeout=60.0)
        self.application = Application.builder().token(self.config.bot_token).request(request).build()

        # تهيئة مدير المعاملات
        await transaction_manager.initialize()

        # تهيئة وحدة الدردشة مع الذكاء الاصطناعي
        try:
            import ai_chat
            ai_chat.initialize(api_manager)
            logger.info("✅ تم تهيئة وحدة الدردشة مع الذكاء الاصطناعي")
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة وحدة الدردشة مع الذكاء الاصطناعي: {str(e)}")

        # إضافة معالجات الأوامر الأساسية
        self.application.add_handler(CommandHandler("start", start))  # أمر البداية للمستخدمين الجدد
        # إضافة معالج لأزرار الشروط والأحكام واختيار اللغة
        self.application.add_handler(CallbackQueryHandler(button_click, pattern='^(terms_agree|terms_decline|set_initial_lang_ar|set_initial_lang_en)$'))
        self.application.add_handler(CommandHandler("help", help_command))  # أمر المساعدة وعرض الأوامر المتاحة
        self.application.add_handler(CommandHandler("alert", alert_command))  # إعداد تنبيهات الأسعار
        self.application.add_handler(CommandHandler("analyze", analyze_command))  # تحليل الرسوم البيانية

        # أوامر إدارة الاشتراكات
        self.application.add_handler(CommandHandler("reset", subscription_system.reset_user_data))  # إعادة تعيين بيانات المستخدم
        self.application.add_handler(CommandHandler("set_subscription", subscription_system.set_subscription_status))  # تعيين حالة الاشتراك
        self.application.add_handler(CommandHandler("stats", show_user_stats))  # عرض إحصائيات المستخدم

        # أوامر إدارة API
        self.application.add_handler(CommandHandler("setup_api", api_setup_command))  # إعداد مفاتيح API
        self.application.add_handler(CommandHandler("api_info", api_info_command))  # عرض معلومات API
        self.application.add_handler(CommandHandler("delete_api", delete_api_command))  # حذف مفاتيح API

        # أوامر إدارة النظام
        self.application.add_handler(CommandHandler("stop_tasks", stop_all_scheduled_tasks))  # إيقاف المهام المجدولة
        self.application.add_handler(CommandHandler("backup", perform_backup))  # إنشاء نسخة احتياطية
        self.application.add_handler(CommandHandler("system_info", system_info))  # معلومات النظام
        self.application.add_handler(CommandHandler("cleanup", cleanup_system))  # تنظيف النظام
        self.application.add_handler(CommandHandler("free_day", grant_free_day_command))  # منح يوم مجاني

        # أوامر المشرف
        self.application.add_handler(CommandHandler("cast", cast))  # إرسال رسالة جماعية
        self.application.add_handler(CommandHandler("ban", ban_user))  # حظر مستخدم
        self.application.add_handler(CommandHandler("unban", unban_user))  # إلغاء حظر مستخدم

        # --- معالجات تعليم التداول بالذكاء الاصطناعي ---
        self.application.add_handler(CommandHandler("learn_trading_ai", handle_learn_trading_ai))
        # معالج لاستعلامات الأزرار داخل وحدة التعليم
        self.application.add_handler(CallbackQueryHandler(handle_trading_education_callback, pattern='^(next_chapter_|start_quiz|ask_ai_tutor|add_gemini_key|learn_trading_ai|supplementary_chapters|supplementary_chapter_|back_to_quiz_results)')) # إضافة معالجة لزر تعلم التداول
        # معالج للرسائل النصية عندما يكون المستخدم في وضع "اسأل الذكاء الاصطناعي"
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_ai_tutor_message_wrapper))

        # معالجات التفاعل
        self.application.add_handler(CallbackQueryHandler(extend_transaction, pattern=r"^extend_transaction_"))  # معالجة تمديد المعاملة
        self.application.add_handler(CallbackQueryHandler(complete_payment, pattern=r"^complete_payment_"))  # معالجة إكمال الدفع
        self.application.add_handler(CallbackQueryHandler(verify_payment, pattern=r"^verify_payment_"))  # معالجة التحقق من الدفع
        self.application.add_handler(CallbackQueryHandler(button_click))  # معالجة النقر على الأزرار
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))  # معالجة الرسائل النصية

        # معالج إجابات استطلاعات الرأي (للاختبارات)
        self.application.add_handler(PollAnswerHandler(handle_quiz_answer))

        # إضافة معالج الأخطاء
        self.application.add_error_handler(error_handler)

        # جدولة المهام
        job_queue = self.application.job_queue
        job_queue.run_repeating(check_alerts, interval=300)
        job_queue.run_repeating(perform_backup, interval=86400)
        job_queue.run_repeating(cleanup_pending_transactions, interval=3600)
        job_queue.run_repeating(check_expired_subscriptions, interval=3600)
        job_queue.run_repeating(notify_expiring_subscriptions, interval=86400)  # التحقق كل يوم
        job_queue.run_repeating(send_daily_report, interval=43200)  # إرسال تقرير للمالك كل 12 ساعة
        job_queue.run_repeating(notify_expiring_transactions, interval=900)  # التحقق كل 15 دقيقة

        # مهمة تنظيف البيانات المؤقتة منتهية الصلاحية
        async def cleanup_expired_cache(context: CallbackContext):
            try:
                deleted_count = firestore_cache.clear_expired()
                logger.info(f"تم حذف {deleted_count} من البيانات المؤقتة منتهية الصلاحية")
            except Exception as e:
                logger.error(f"خطأ في تنظيف البيانات المؤقتة: {str(e)}")

        # تشغيل مهمة تنظيف البيانات المؤقتة كل 6 ساعات
        job_queue.run_repeating(cleanup_expired_cache, interval=21600, first=300)  # كل 6 ساعات، بعد 5 دقائق من بدء التشغيل

        # تشغيل مهمة التحقق من وتحديث حالة الأيام المجانية كل ساعة
        async def check_free_days(context: CallbackContext):
            """التحقق من وتحديث حالة الأيام المجانية"""
            try:
                logger.info("🔄 جاري التحقق من وتحديث حالة الأيام المجانية...")
                activated, deactivated = await free_day_system.check_and_update_free_days()
                logger.info(f"✅ تم تفعيل اليوم المجاني لـ {activated} مستخدم وإلغاء تفعيل اليوم المجاني لـ {deactivated} مستخدم")
            except Exception as e:
                logger.error(f"❌ خطأ في التحقق من وتحديث حالة الأيام المجانية: {str(e)}")

        # تشغيل مهمة إرسال تذكيرات اليوم المجاني كل يوم
        async def send_free_day_reminders(context: CallbackContext):
            """إرسال تذكيرات اليوم المجاني"""
            try:
                logger.info("🔄 جاري إرسال تذكيرات اليوم المجاني...")
                reminder_count = await free_day_system.send_free_day_reminders()
                logger.info(f"✅ تم إرسال تذكيرات اليوم المجاني لـ {reminder_count} مستخدم")
            except Exception as e:
                logger.error(f"❌ خطأ في إرسال تذكيرات اليوم المجاني: {str(e)}")

        # تشغيل مهمة تنظيف إشعارات اليوم المجاني القديمة كل يوم
        async def cleanup_free_day_notifications(context: CallbackContext):
            """تنظيف إشعارات اليوم المجاني القديمة"""
            try:
                logger.info("🔄 جاري تنظيف إشعارات اليوم المجاني القديمة...")
                deleted_count = await free_day_system.cleanup_free_day_notifications(days_old=7)
                logger.info(f"✅ تم حذف {deleted_count} إشعار يوم مجاني قديم")
            except Exception as e:
                logger.error(f"❌ خطأ في تنظيف إشعارات اليوم المجاني القديمة: {str(e)}")

        # تشغيل مهمة التحقق من الأيام المجانية كل ساعة
        job_queue.run_repeating(check_free_days, interval=3600, first=60)  # كل ساعة، بعد دقيقة من بدء التشغيل

        # تشغيل مهمة إرسال تذكيرات اليوم المجاني كل يوم في الساعة 9 صباحاً
        job_queue.run_daily(send_free_day_reminders, time=datetime_time(hour=9, minute=0, second=0))
        
        # تشغيل مهمة تنظيف إشعارات اليوم المجاني القديمة كل يوم في الساعة 3 صباحاً
        job_queue.run_daily(cleanup_free_day_notifications, time=datetime_time(hour=3, minute=0, second=0))



        logger.info("✅ تم تهيئة البوت بنجاح")

    async def start(self):
        """بدء تشغيل البوت"""
        try:
            logger.info("🚀 جاري بدء تشغيل البوت...")
            await self.application.initialize()
            await self.application.start()
            self._running = True

            # بدء الاستطلاع في الخلفية
            await self.application.updater.start_polling(
                allowed_updates=Update.ALL_TYPES,
                drop_pending_updates=True
            )
            logger.info("✅ تم بدء تشغيل البوت بنجاح")

            # إضافة مجدول للتحقق من الإعدادات كل 6 ساعات
            self.application.job_queue.run_repeating(
                self._check_settings_periodically,
                interval=21600,  # كل 6 ساعات
                first=21600      # بعد 6 ساعات من بدء التشغيل
            )

            # العودة بمجرد بدء البوت بنجاح بدلاً من الانتظار في حلقة لا نهائية
            return

        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل البوت: {str(e)}")
            raise
        finally:
            # لا نقوم بإيقاف البوت هنا لأننا نريده أن يستمر في العمل
            pass

    async def run_forever(self):
        """تشغيل البوت إلى أجل غير مسمى"""
        try:
            # الانتظار حتى يتم إيقاف البوت
            while self._running:
                await asyncio.sleep(1)
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل البوت المستمر: {str(e)}")
        finally:
            await self.stop()

    async def _check_settings_periodically(self, context: CallbackContext):
        """التحقق من الإعدادات بشكل دوري"""
        try:
            logger.info("🔄 جاري التحقق من الإعدادات بشكل دوري...")

            # التحقق من الإعدادات العامة
            general_settings = system_settings.get_all()
            for key, value in general_settings.items():
                # التحقق مما إذا كان الإعداد يجب أن يكون حساسًا
                if any(keyword in key for keyword in ['API', 'KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'CREDENTIAL', 'AUTH']):
                    logger.info(f"⚠️ تم العثور على إعداد حساس في الوثيقة العامة: {key}")
                    # نقل الإعداد إلى الوثيقة الحساسة
                    system_settings.set(key, value, sensitive=True)
                    system_settings.delete(key)
                    logger.info(f"✅ تم نقل الإعداد {key} إلى الوثيقة الحساسة")

            # التحقق من الإعدادات الحساسة
            sensitive_settings = system_settings.get_all(sensitive=True)
            for key, value in sensitive_settings.items():
                # التحقق مما إذا كان الإعداد يجب أن يكون عامًا
                if not any(keyword in key for keyword in ['API', 'KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'CREDENTIAL', 'AUTH']):
                    logger.info(f"⚠️ تم العثور على إعداد عام في الوثيقة الحساسة: {key}")
                    # نقل الإعداد إلى الوثيقة العامة
                    system_settings.set(key, value, sensitive=False)
                    system_settings.delete(key, sensitive=True)
                    logger.info(f"✅ تم نقل الإعداد {key} إلى الوثيقة العامة")

            logger.info("✅ تم التحقق من الإعدادات بنجاح")
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الإعدادات: {str(e)}")

    async def stop(self):
        """إيقاف البوت بشكل آمن"""
        if self._running:
            try:
                logger.info("🛑 جاري إيقاف البوت...")
                self._running = False
                if self.application.updater.running:
                    await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
                logger.info("✅ تم إيقاف البوت بنجاح")
            except Exception as e:
                logger.error(f"❌ خطأ في إيقاف البوت: {str(e)}")

# دوال الميزات المتقدمة
async def get_trading_strategy_analysis(update: Update, context: CallbackContext, symbol: str):
    """
    الحصول على استراتيجية تداول آلية للعملة المحددة
    """
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # استخدام العملة المخصصة إذا كانت متوفرة
        custom_currencies = settings.get('currencies', [])
        if custom_currencies:
            target_currency = custom_currencies[0]
        else:
            target_currency = 'USD'  # العملة الافتراضية

        # التحقق من حالة الاشتراك
        if not subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                "This feature is available for subscribers only",
                show_alert=True
            )
            return

        # إرسال رسالة انتظار
        wait_message = await update.callback_query.message.reply_text(
            "جاري تحليل البيانات وإنشاء استراتيجية تداول..." if lang == 'ar' else
            "Analyzing data and creating trading strategy..."
        )

        # الحصول على بيانات السوق مع تمرير العملة المفضلة
        market_data = await ca.get_market_data(symbol, target_currency=target_currency, user_id=user_id, lang=lang)

        if not market_data:
            await wait_message.edit_text(
                f"لم يتم العثور على بيانات لـ {symbol}" if lang == 'ar' else
                f"Could not find data for {symbol}"
            )
            return

        # الحصول على نموذج Gemini للمستخدم
        from analysis.gemini_analysis import get_user_api_client, get_trading_strategy
        gemini_model = await get_user_api_client(user_id, 'gemini')

        if not gemini_model:
            # إذا لم يكن هناك مفتاح API، إظهار رسالة للمستخدم
            await wait_message.edit_text(
                "يرجى إضافة مفتاح Gemini API الخاص بك أولاً. انتقل إلى إعدادات API من القائمة الرئيسية." if lang == 'ar' else
                "Please add your Gemini API key first. Go to API Setup from the main menu."
            )

            # توجيه المستخدم إلى صفحة إعدادات API
            await show_api_instructions(update, context, 'gemini', lang)
            return

        # الحصول على استراتيجية التداول
        strategy_text = await get_trading_strategy(gemini_model, market_data, lang)

        if not strategy_text:
            await wait_message.edit_text(
                "حدث خطأ أثناء إنشاء استراتيجية التداول" if lang == 'ar' else
                "An error occurred while creating the trading strategy"
            )
            return

        # إنشاء أزرار التحكم
        keyboard = [
            [
                InlineKeyboardButton("🔄 تحديث" if lang == 'ar' else "🔄 Refresh",
                                   callback_data=f'trading_strategy_{symbol}')
            ],
            [
                InlineKeyboardButton("🔙 رجوع" if lang == 'ar' else "🔙 Back",
                                   callback_data='back_to_main')
            ]
        ]

        # إرسال الاستراتيجية
        await wait_message.edit_text(
            text=strategy_text,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        logger.error(f"خطأ في الحصول على استراتيجية تداول: {str(e)}")
        try:
            await wait_message.edit_text(
                f"حدث خطأ: {str(e)}" if lang == 'ar' else
                f"An error occurred: {str(e)}"
            )
        except:
            await update.callback_query.answer(
                "حدث خطأ أثناء معالجة الطلب" if lang == 'ar' else
                "An error occurred while processing the request",
                show_alert=True
            )


async def get_price_prediction_analysis(update: Update, context: CallbackContext, symbol: str):
    """
    الحصول على تنبؤات سعرية للعملة المحددة
    """
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # استخدام العملة المخصصة إذا كانت متوفرة
        custom_currencies = settings.get('currencies', [])
        if custom_currencies:
            target_currency = custom_currencies[0]
        else:
            target_currency = 'USD'  # العملة الافتراضية

        # التحقق من حالة الاشتراك
        if not subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                "This feature is available for subscribers only",
                show_alert=True
            )
            return

        # إرسال رسالة انتظار
        wait_message = await update.callback_query.message.reply_text(
            "جاري تحليل البيانات وإنشاء تنبؤات سعرية..." if lang == 'ar' else
            "Analyzing data and creating price predictions..."
        )

        # الحصول على بيانات السوق مع تمرير العملة المفضلة
        market_data = await ca.get_market_data(symbol, target_currency=target_currency)

        if not market_data:
            await wait_message.edit_text(
                f"لم يتم العثور على بيانات لـ {symbol}" if lang == 'ar' else
                f"Could not find data for {symbol}"
            )
            return

        # الحصول على نموذج Gemini للمستخدم
        from analysis.gemini_analysis import get_user_api_client, get_price_prediction
        gemini_model = await get_user_api_client(user_id, 'gemini')

        if not gemini_model:
            # إذا لم يكن هناك مفتاح API، إظهار رسالة للمستخدم
            await wait_message.edit_text(
                "يرجى إضافة مفتاح Gemini API الخاص بك أولاً. انتقل إلى إعدادات API من القائمة الرئيسية." if lang == 'ar' else
                "Please add your Gemini API key first. Go to API Setup from the main menu."
            )

            # توجيه المستخدم إلى صفحة إعدادات API
            await show_api_instructions(update, context, 'gemini', lang)
            return

        # الحصول على التنبؤات السعرية
        prediction_text = await get_price_prediction(gemini_model, market_data, lang)

        if not prediction_text:
            await wait_message.edit_text(
                "حدث خطأ أثناء إنشاء التنبؤات السعرية" if lang == 'ar' else
                "An error occurred while creating price predictions"
            )
            return

        # إنشاء أزرار التحكم
        keyboard = [
            [
                InlineKeyboardButton("🔄 تحديث" if lang == 'ar' else "🔄 Refresh",
                                   callback_data=f'price_prediction_{symbol}')
            ],
            [
                InlineKeyboardButton("🔙 رجوع" if lang == 'ar' else "🔙 Back",
                                   callback_data='back_to_main')
            ]
        ]

        # إرسال التنبؤات
        await wait_message.edit_text(
            text=prediction_text,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        logger.error(f"خطأ في الحصول على تنبؤات سعرية: {str(e)}")
        try:
            await wait_message.edit_text(
                f"حدث خطأ: {str(e)}" if lang == 'ar' else
                f"An error occurred: {str(e)}"
            )
        except:
            await update.callback_query.answer(
                "حدث خطأ أثناء معالجة الطلب" if lang == 'ar' else
                "An error occurred while processing the request",
                show_alert=True
            )


async def get_ichimoku_cloud_analysis(update: Update, context: CallbackContext, symbol: str):
    """
    الحصول على تحليل مؤشر سحابة إيشيموكو (Ichimoku Cloud) للعملة المحددة
    """
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # استخدام العملة المخصصة إذا كانت متوفرة
        custom_currencies = settings.get('currencies', [])
        if custom_currencies:
            target_currency = custom_currencies[0]
        else:
            target_currency = 'USD'  # العملة الافتراضية

        # التحقق من حالة الاشتراك
        if not subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                "This feature is available for subscribers only",
                show_alert=True
            )
            return

        # إرسال رسالة انتظار
        wait_message = await update.callback_query.message.reply_text(
            "جاري تحليل مؤشر سحابة إيشيموكو..." if lang == 'ar' else
            "Analyzing Ichimoku Cloud indicator..."
        )

        # الحصول على بيانات السوق مع تمرير العملة المفضلة
        market_data = await ca.get_market_data(symbol, target_currency=target_currency, user_id=user_id, lang=lang)

        if not market_data:
            await wait_message.edit_text(
                f"لم يتم العثور على بيانات لـ {symbol}" if lang == 'ar' else
                f"Could not find data for {symbol}"
            )
            return

        # الحصول على نموذج Gemini للمستخدم
        from analysis.gemini_analysis import get_user_api_client, get_ichimoku_analysis
        gemini_model = await get_user_api_client(user_id, 'gemini')

        if not gemini_model:
            # إذا لم يكن هناك مفتاح API، إظهار رسالة للمستخدم
            await wait_message.edit_text(
                "يرجى إضافة مفتاح Gemini API الخاص بك أولاً. انتقل إلى إعدادات API من القائمة الرئيسية." if lang == 'ar' else
                "Please add your Gemini API key first. Go to API Setup from the main menu."
            )

            # توجيه المستخدم إلى صفحة إعدادات API
            await show_api_instructions(update, context, 'gemini', lang)
            return

        # استخراج قيم مؤشر إيشيموكو من بيانات السوق
        ichimoku_data = {
            'tenkan': market_data.get('ichimoku_tenkan', float('nan')),
            'kijun': market_data.get('ichimoku_kijun', float('nan')),
            'senkou_a': market_data.get('ichimoku_senkou_a', float('nan')),
            'senkou_b': market_data.get('ichimoku_senkou_b', float('nan')),
            'chikou': market_data.get('ichimoku_chikou', float('nan')),
            'price': market_data.get('price', float('nan')),
            'symbol': market_data.get('symbol', symbol)
        }

        # الحصول على تحليل مؤشر إيشيموكو
        analysis_text = await get_ichimoku_analysis(gemini_model, market_data, ichimoku_data, lang)

        if not analysis_text:
            await wait_message.edit_text(
                "حدث خطأ أثناء إنشاء تحليل مؤشر سحابة إيشيموكو" if lang == 'ar' else
                "An error occurred while creating Ichimoku Cloud analysis"
            )
            return

        # إنشاء أزرار التحكم
        keyboard = [
            [
                InlineKeyboardButton("🔄 تحديث" if lang == 'ar' else "🔄 Refresh",
                                   callback_data=f'ichimoku_cloud_{symbol}')
            ],
            [
                InlineKeyboardButton("🔙 رجوع" if lang == 'ar' else "🔙 Back",
                                   callback_data='back_to_main')
            ]
        ]

        # إرسال التحليل
        await wait_message.edit_text(
            text=analysis_text,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        logger.error(f"خطأ في الحصول على تحليل مؤشر سحابة إيشيموكو: {str(e)}")
        try:
            await wait_message.edit_text(
                f"حدث خطأ: {str(e)}" if lang == 'ar' else
                f"An error occurred: {str(e)}"
            )
        except:
            await update.callback_query.answer(
                "حدث خطأ أثناء معالجة الطلب" if lang == 'ar' else
                "An error occurred while processing the request",
                show_alert=True
            )


async def get_multi_timeframe_analysis_view(update: Update, context: CallbackContext, symbol: str):
    """
    الحصول على تحليل متعدد الإطارات الزمنية للعملة المحددة
    """
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # استخدام العملة المخصصة إذا كانت متوفرة
        custom_currencies = settings.get('currencies', [])
        if custom_currencies:
            target_currency = custom_currencies[0]
        else:
            target_currency = 'USD'  # العملة الافتراضية

        # التحقق من حالة الاشتراك
        if not subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                "This feature is available for subscribers only",
                show_alert=True
            )
            return

        # إرسال رسالة انتظار
        wait_message = await update.callback_query.message.reply_text(
            "جاري تحليل البيانات عبر إطارات زمنية متعددة..." if lang == 'ar' else
            "Analyzing data across multiple timeframes..."
        )

        # الحصول على بيانات السوق للإطار الزمني الرئيسي (4 ساعات) مع تمرير العملة المفضلة
        market_data = await ca.get_market_data(symbol, target_currency=target_currency)

        if not market_data:
            await wait_message.edit_text(
                f"لم يتم العثور على بيانات لـ {symbol}" if lang == 'ar' else
                f"Could not find data for {symbol}"
            )
            return

        # الحصول على بيانات الإطارات الزمنية المختلفة
        timeframes = {
            '1h': None,
            '4h': market_data,  # استخدام البيانات التي تم جلبها بالفعل
            '1d': None,
            '1w': None
        }

        # محاولة جلب بيانات الإطارات الزمنية الأخرى
        try:
            # استخدام BinanceAPIManager للحصول على البيانات مع تمرير العملة المفضلة
            timeframes['1h'] = await ca.get_market_data(symbol, interval='1h', target_currency=target_currency)
            timeframes['1d'] = await ca.get_market_data(symbol, interval='1d', target_currency=target_currency)
            timeframes['1w'] = await ca.get_market_data(symbol, interval='1w', target_currency=target_currency)
        except Exception as e:
            logger.warning(f"لم يتم الحصول على جميع الإطارات الزمنية: {str(e)}")
            # استمر مع البيانات المتاحة

        # تصفية الإطارات الزمنية التي تم الحصول عليها بنجاح
        valid_timeframes = {k: v for k, v in timeframes.items() if v is not None}

        if len(valid_timeframes) < 2:
            await wait_message.edit_text(
                "لم يتم الحصول على بيانات كافية للإطارات الزمنية المختلفة" if lang == 'ar' else
                "Could not get enough data for different timeframes"
            )
            return

        # الحصول على نموذج Gemini للمستخدم
        from analysis.gemini_analysis import get_user_api_client, get_multi_timeframe_analysis
        gemini_model = await get_user_api_client(user_id, 'gemini')

        if not gemini_model:
            # إذا لم يكن هناك مفتاح API، إظهار رسالة للمستخدم
            await wait_message.edit_text(
                "يرجى إضافة مفتاح Gemini API الخاص بك أولاً. انتقل إلى إعدادات API من القائمة الرئيسية." if lang == 'ar' else
                "Please add your Gemini API key first. Go to API Setup from the main menu."
            )

            # توجيه المستخدم إلى صفحة إعدادات API
            await show_api_instructions(update, context, 'gemini', lang)
            return

        # الحصول على التحليل متعدد الإطارات الزمنية
        analysis_text = await get_multi_timeframe_analysis(gemini_model, market_data, valid_timeframes, lang)

        if not analysis_text:
            await wait_message.edit_text(
                "حدث خطأ أثناء إنشاء التحليل متعدد الإطارات الزمنية" if lang == 'ar' else
                "An error occurred while creating multi-timeframe analysis"
            )
            return

        # إنشاء أزرار التحكم
        keyboard = [
            [
                InlineKeyboardButton("🔄 تحديث" if lang == 'ar' else "🔄 Refresh",
                                   callback_data=f'multi_timeframe_{symbol}')
            ],
            [
                InlineKeyboardButton("🔙 رجوع" if lang == 'ar' else "🔙 Back",
                                   callback_data='back_to_main')
            ]
        ]

        # إرسال التحليل
        await wait_message.edit_text(
            text=analysis_text,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        logger.error(f"خطأ في الحصول على تحليل متعدد الإطارات الزمنية: {str(e)}")
        try:
            await wait_message.edit_text(
                f"حدث خطأ: {str(e)}" if lang == 'ar' else
                f"An error occurred: {str(e)}"
            )
        except:
            await update.callback_query.answer(
                "حدث خطأ أثناء معالجة الطلب" if lang == 'ar' else
                "An error occurred while processing the request",
                show_alert=True
            )


# --- وظائف مساعدة لمعالجات تعليم التداول ---
async def handle_trading_education_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """يعالج استعلامات الأزرار من وحدة تعليم التداول."""
    query = update.callback_query
    await query.answer() # مهم لإعلام تيليجرام بأن الزر تم التعامل معه
    user_id = str(query.from_user.id)
    data = query.data
    logger.info(f"Received trading education callback: {data} from user {user_id}")

    # استرجاع لغة المستخدم
    settings = subscription_system.get_user_settings(user_id)
    lang = settings.get('lang', 'ar')

    if data.startswith('next_chapter_'):
        try:
            chapter_number = int(data.split('_')[-1])
            # نحتاج إلى تعيين حالة 'is_asking_ai' إلى False عند التنقل
            if user_id in user_education_state:
                user_education_state[user_id]['is_asking_ai'] = False
            await generate_and_send_chapter(update, context, user_id, chapter_number, lang)
        except (ValueError, IndexError) as e:
            logger.error(f"خطأ في تحليل بيانات زر الفصل التالي: {data} - {e}")
            try:
                await query.edit_message_text("حدث خطأ ما. يرجى المحاولة مرة أخرى.")
            except Exception as edit_error:
                 logger.error(f"Failed to edit message after chapter error: {edit_error}")
    elif data == 'start_quiz':
         if user_id in user_education_state:
             user_education_state[user_id]['is_asking_ai'] = False
         await start_quiz(update, context, user_id, lang)
    elif data == 'ask_ai_tutor':
        # تعيين الحالة للسماح بمعالج الرسائل بالتقاط السؤال التالي
        if user_id in user_education_state:
            user_education_state[user_id]['is_asking_ai'] = True
            logger.info(f"User {user_id} set to 'is_asking_ai' state.")
        else:
             # قد يحتاج المستخدم إلى بدء الدورة أولاً
             logger.warning(f"User {user_id} tried to ask AI without education state.")
             try:
                 # التأكد من أن query.message ليس None قبل محاولة التعديل
                 if query.message:
                     await query.edit_message_text("يرجى بدء دورة تعلم التداول أولاً باستخدام /learn_trading_ai")
                 else:
                     # إرسال رسالة جديدة إذا لم يكن هناك رسالة لتعديلها
                     await context.bot.send_message(chat_id=query.effective_chat.id, text="يرجى بدء دورة تعلم التداول أولاً باستخدام /learn_trading_ai")
             except Exception as edit_error:
                 logger.error(f"Failed to edit or send message for ask AI without state: {edit_error}")
             return
        await handle_ask_ai_tutor_button(update, context, user_id, lang)
    elif data == 'learn_trading_ai': # معالجة زر تعلم التداول بالذكاء الاصطناعي
        # التحقق من وجود مفتاح Gemini API
        from analysis.gemini_analysis import get_user_api_client
        model = await get_user_api_client(user_id, 'gemini')
        if not model:
            # إذا لم يكن هناك مفتاح API، توجيه المستخدم لإضافة المفتاح
            await query.answer(
                "يرجى إضافة مفتاح Gemini API أولاً للاستفادة من ميزة التعلم بالذكاء الاصطناعي" if lang == 'ar' else
                "Please add your Gemini API key first to use the AI learning feature",
                show_alert=True
            )
            # توجيه المستخدم لإعداد المفتاح
            await api_setup_command(update, context, preselect_platform='gemini')
            return

        # إذا كان هناك مفتاح API، متابعة العملية
        await handle_learn_trading_ai(update, context)
    elif data == 'add_gemini_key':
         # توجيه المستخدم لإعداد المفتاح
         # التأكد من أن query.message ليس None
         target_update = query if query.message else update # استخدام update إذا لم يكن هناك query.message
         await api_setup_command(target_update, context, preselect_platform='gemini')
         # يمكنك اختياريًا إزالة الرسالة الأصلية أو تعديلها
         try:
             if query.message:
                 await query.edit_message_text("يرجى اتباع التعليمات لإضافة مفتاح Gemini API الخاص بك.")
         except Exception as edit_error:
             logger.error(f"Failed to edit message for add_gemini_key: {edit_error}")
    elif data == 'supplementary_chapters':
        # معالجة زر الفصول التكميلية
        from education.trading_education import show_supplementary_chapters
        logger.info(f"Handling supplementary_chapters button for user {user_id}")
        await show_supplementary_chapters(update, context, user_id, lang)
    elif data.startswith('supplementary_chapter_'):
        # معالجة اختيار فصل تكميلي محدد
        from education.trading_education import generate_and_send_supplementary_chapter
        chapter_id = data.replace('supplementary_chapter_', '')
        logger.info(f"Handling supplementary_chapter_{chapter_id} button for user {user_id}")
        await generate_and_send_supplementary_chapter(update, context, user_id, chapter_id, lang)
    elif data == 'back_to_quiz_results':
        # العودة إلى نتائج الاختبار
        from education.trading_education import show_quiz_results_or_next_steps
        logger.info(f"Handling back_to_quiz_results button for user {user_id}")
        await show_quiz_results_or_next_steps(update, context, user_id, lang)
    else:
        logger.warning(f"تم تلقي استعلام غير معروف لوحدة التعليم: {data}")

async def handle_ai_tutor_message_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """غلاف لمعالج رسائل مدرس الذكاء الاصطناعي للتحقق من الحالة."""
    user_id = str(update.effective_user.id)
    message_text = update.message.text

    # تحقق مما إذا كان المستخدم في حالة "طرح سؤال"
    # يجب تحسين هذا لاحقًا باستخدام Firestore أو context.user_data
    if user_id in user_education_state and user_education_state[user_id].get('is_asking_ai'):
        logger.info(f"User {user_id} is in 'is_asking_ai' state, handling message: {message_text}")
        await handle_message_for_ai_tutor(update, context)
        # اختياري: إعادة تعيين الحالة بعد الإجابة للسماح بمتابعة الدروس
        # user_education_state[user_id]['is_asking_ai'] = False
        # logger.info(f"Reset 'is_asking_ai' state for user {user_id} after handling message.")
    else:
        # إذا لم يكن المستخدم في حالة طرح سؤال، مرر الرسالة للمعالج العام
        logger.debug(f"User {user_id} not in 'is_asking_ai' state, passing message to general handler: {message_text}")
        await handle_message(update, context) # استدعاء المعالج العام للرسائل

# --- نهاية وظائف المساعدة ---

async def run_bot():
    """تشغيل البوت الرئيسي"""
    try:
        # نقل الإعدادات من ملف التكوين إلى قاعدة البيانات
        await migrate_config_to_database()

        # تهيئة مدير المعاملات
        transaction_manager = TransactionManager()
        await transaction_manager.initialize()

        # تهيئة نظام التحقق التلقائي من المدفوعات
        from integrations.paypal_payment import AutomaticPaymentVerifier

        # استخدام الإعدادات من قاعدة البيانات
        paypal_client_id = system_settings.get("PAYPAL_CLIENT_ID", sensitive=True)
        paypal_secret = system_settings.get("PAYPAL_CLIENT_SECRET", sensitive=True)
        is_sandbox = system_settings.get("PAYPAL_SANDBOX_MODE", False)  # تغيير القيمة الافتراضية إلى False (وضع الإنتاج)

        # إذا لم تكن الإعدادات موجودة في قاعدة البيانات، نستخدم المتغيرات البيئية
        if not paypal_client_id:
            paypal_client_id = SystemConfig.get_env_var("PAYPAL_CLIENT_ID")
            system_settings.set("PAYPAL_CLIENT_ID", paypal_client_id, sensitive=True)

        if not paypal_secret:
            paypal_secret = SystemConfig.get_env_var("PAYPAL_CLIENT_SECRET")
            system_settings.set("PAYPAL_CLIENT_SECRET", paypal_secret, sensitive=True)

        if is_sandbox is None:
            is_sandbox = SystemConfig.get_env_var("PAYPAL_SANDBOX_MODE", "false").lower() == "true"  # تغيير القيمة الافتراضية إلى false
            system_settings.set("PAYPAL_SANDBOX_MODE", is_sandbox)

        payment_verifier = AutomaticPaymentVerifier(
            db=db,
            paypal_client_id=paypal_client_id,
            paypal_secret=paypal_secret,
            is_sandbox=is_sandbox,
            bot_token=TOKEN  # إضافة رمز البوت لإرسال الإشعارات
        )

        # بدء نظام التحقق التلقائي
        # استخدام منطقة زمنية من مكتبة pytz
        await payment_verifier.start()
        logger.info("✅ تم بدء نظام التحقق التلقائي من المدفوعات")

        # تم تعطيل مجدول تشغيل الرابط في هذا الملف لأنه يتم تنفيذه في ملف main_wrapper.py
        logger.info("ℹ️ مجدول تشغيل رابط فحص الصحة يتم إدارته بواسطة main_wrapper.py")

        # إنشاء وتشغيل البوت
        logger.info("🤖 جاري إنشاء البوت...")
        bot = TelegramBot()

        logger.info("🔧 جاري إعداد البوت...")
        await bot.setup()

        logger.info("🚀 جاري بدء تشغيل البوت (قد يستغرق هذا بعض الوقت)...")

        # بدء تشغيل البوت (بدون انتظار انتهاء الدالة)
        await bot.start()

        # تشغيل البوت إلى أجل غير مسمى
        logger.info("🔄 جاري تشغيل البوت بشكل مستمر...")
        await bot.run_forever()

    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {str(e)}")
        raise e

async def migrate_config_to_database():
    """نقل الإعدادات من ملف التكوين إلى قاعدة البيانات"""
    try:
        # قائمة بالإعدادات الحساسة
        sensitive_keys = [
            "PAYPAL_CLIENT_ID",
            "PAYPAL_CLIENT_SECRET",
            "BOT_TOKEN",
            # تم تعطيل استيراد مفاتيح Binance API
            # "BINANCE_API_KEY",
            # "BINANCE_API_SECRET",
            # تم تعطيل استيراد مفتاح Gemini API
            # "GEMINI_API_KEY",
            "ENCRYPTION_KEY",
            "GITHUB_TOKEN"
        ]

        # التحقق من وجود الإعدادات في قاعدة البيانات
        settings_count = len(system_settings.get_all())
        sensitive_count = len(system_settings.get_all(sensitive=True))

        # نقوم بإعادة تعيين الإعدادات فقط إذا كانت هناك حاجة لذلك
        # إذا كان هناك إعدادات حساسة في الوثيقة العامة أو العكس، نقوم بإعادة تعيينها
        need_reset = False

        # التحقق من الإعدادات العامة
        general_settings = system_settings.get_all()
        for key, value in general_settings.items():
            # التحقق مما إذا كان الإعداد يجب أن يكون حساسًا
            if any(keyword in key for keyword in ['API', 'KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'CREDENTIAL', 'AUTH']):
                logger.info(f"⚠️ تم العثور على إعداد حساس في الوثيقة العامة: {key}")
                need_reset = True
                break

        # التحقق من الإعدادات الحساسة
        sensitive_settings = system_settings.get_all(sensitive=True)
        for key, value in sensitive_settings.items():
            # التحقق مما إذا كان الإعداد يجب أن يكون عامًا
            if not any(keyword in key for keyword in ['API', 'KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'CREDENTIAL', 'AUTH']):
                logger.info(f"⚠️ تم العثور على إعداد عام في الوثيقة الحساسة: {key}")
                need_reset = True
                break

        # إذا كانت هناك حاجة لإعادة تعيين الإعدادات أو لم تكن هناك إعدادات
        if need_reset or settings_count == 0 or sensitive_count == 0:
            # استيراد فقط متغيرات البيئة المحددة في ملف .env
            # قائمة المتغيرات المسموح بها
            allowed_env_vars = [
                # Bot Configuration
                "DEVELOPER_ID", "BOT_TOKEN",

                # Binance API - تم تعطيل استيراد مفاتيح Binance API
                # "BINANCE_API_KEY", "BINANCE_API_SECRET",

                # Gemini API - تم تعطيل استيراد مفتاح Gemini API
                # "GEMINI_API_KEY",

                # GitHub Configuration
                "GITHUB_TOKEN", "GITHUB_REPO", "GITHUB_OWNER",

                # Firebase Configuration
                "GOOGLE_APPLICATION_CREDENTIALS",

                # Default Settings
                "ANALYSES_PER_DAY", "ALERTS_PER_DAY", "DEFAULT_LANG",

                # System Settings
                "BACKUP_INTERVAL", "ENCRYPTION_KEY",

                # Health Check
                "HEALTH_CHECK_TOKEN", "HEALTH_CHECK_PORT",

                # Payment Settings
                "PAYMENT_AMOUNT",

                # Update Settings
                "AUTO_UPDATE", "UPDATE_CHANNEL",

                # Secret Key
                "SECRET_KEY",

                # PayPal Configuration
                "PAYPAL_CLIENT_ID", "PAYPAL_CLIENT_SECRET", "DEV_MODE"
            ]

            # حذف جميع الإعدادات الموجودة أولاً
            system_settings.clear_all()
            system_settings.clear_all(sensitive=True)
            logger.info("✅ تم حذف جميع الإعدادات السابقة")

            # استيراد المتغيرات المسموح بها فقط
            local_env_count = 0
            for key in allowed_env_vars:
                if key in os.environ:
                    value = os.environ[key]

                    # تحديد ما إذا كان الإعداد حساسًا أم لا
                    # قائمة البادئات الحساسة
                    sensitive_prefixes = [
                        'PAYPAL_', 'BOT_', 'BINANCE_', 'ENCRYPTION_', 'GITHUB_', 'API_', 'GEMINI_',
                        'SECRET_', 'TOKEN_', 'KEY_', 'PASSWORD_', 'CREDENTIAL_'
                    ]
                    # قائمة الكلمات الحساسة في أي مكان في المفتاح
                    sensitive_keywords = [
                        'API', 'KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'CREDENTIAL', 'AUTH'
                    ]

                    # التحقق من البادئات
                    is_prefix_sensitive = any(key.startswith(prefix) for prefix in sensitive_prefixes)
                    # التحقق من الكلمات الحساسة في أي مكان في المفتاح
                    is_keyword_sensitive = any(keyword in key for keyword in sensitive_keywords)

                    # الإعداد حساس إذا كان يبدأ ببادئة حساسة أو يحتوي على كلمة حساسة
                    is_sensitive = is_prefix_sensitive or is_keyword_sensitive

                    # تحويل القيمة إلى النوع المناسب
                    if value.lower() == 'true':
                        value = True
                    elif value.lower() == 'false':
                        value = False
                    elif value.isdigit():
                        value = int(value)
                    elif value.replace('.', '', 1).isdigit() and value.count('.') == 1:
                        value = float(value)

                    # تعيين الإعداد
                    if system_settings.set(key, value, is_sensitive):
                        local_env_count += 1

            # تعيين علامة التهيئة
            system_settings.set("_initialized", True)

            logger.info(f"✅ تم استيراد {local_env_count} إعداد من متغيرات البيئة المحددة")
        else:
            logger.info(f"✅ تم العثور على {settings_count} إعداد عام و {sensitive_count} إعداد حساس في قاعدة البيانات")

    except Exception as e:
        logger.error(f"❌ خطأ في نقل الإعدادات من ملف التكوين إلى قاعدة البيانات: {str(e)}")
        # في حالة الخطأ، نستمر في تشغيل البوت باستخدام الإعدادات الحالية

def main():
    """النقطة الرئيسية لتشغيل التطبيق"""
    try:
        # تهيئة حلقة الأحداث
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # تعيين مهلة زمنية أطول للعمليات
        logger.info("🔄 جاري تهيئة حلقة الأحداث مع مهلة زمنية أطول...")

        # بدء خادم فحص الصحة للاستضافة على Koyeb
        from server import run_health_server
        health_server_thread = run_health_server()
        logger.info("🌐 تم بدء خادم فحص الصحة للاستضافة على Koyeb")

        # تشغيل البوت
        loop.run_until_complete(run_bot())
        # لا نحتاج إلى loop.run_forever() لأن run_bot() ستستمر إلى أجل غير مسمى

    except KeyboardInterrupt:
        logger.info("تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع: {str(e)}")
        # طباعة تتبع الاستثناء للمساعدة في تشخيص المشكلة
        import traceback
        logger.error(traceback.format_exc())
    finally:
        loop.close()

if __name__ == "__main__":
    main()